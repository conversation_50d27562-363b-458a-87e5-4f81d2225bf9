# callbacks/temperature_rise.py
""" Callbacks para a seção de Elevação de Temperatura. """
from dash import html, Input, Output, State, callback_context, no_update
from utils.callback_helpers import safe_float
import logging
import datetime
from dash.exceptions import PreventUpdate

# Importações da aplicação
from app import app
from config import colors
from utils.routes import normalize_pathname, ROUTE_TEMPERATURE_RISE
from app_core.calculations import (
    calculate_winding_temps,
    calculate_top_oil_rise,
    calculate_thermal_time_constant
)

log = logging.getLogger(__name__)
log.setLevel(logging.DEBUG)

# --- Callback para exibir informações do transformador na página ---
@app.callback(
    Output("transformer-info-temperature-rise-page", "children"),
    Input("transformer-info-temperature-rise", "children"),
    prevent_initial_call=False
)
def update_temperature_rise_page_info_panel(global_panel_content):
    """Copia o conteúdo do painel global para o painel específico da página."""
    log.info("CALLBACK EXECUTADO: Atualizando painel de informações do transformador na página de elevação de temperatura")
    print("CALLBACK EXECUTADO: Atualizando painel de informações do transformador na página de elevação de temperatura")

    # Verificar se o conteúdo do painel global é válido
    if global_panel_content is None:
        log.warning("Conteúdo do painel global é None")
        from components.transformer_info_template import create_transformer_info_panel
        return create_transformer_info_panel({})

    return global_panel_content


# --- Funções Auxiliares (mantida) ---
# --- Callbacks ---

# Callback para CARREGAR dados (RESTAURADO com triggers corretos)
@app.callback(
    [
        Output("temp-amb", "value"),
        Output("winding-material", "value"),
        Output("res-cold", "value"),
        Output("temp-cold", "value"),
        Output("res-hot", "value"),
        Output("temp-top-oil", "value"),
        Output("delta-theta-oil-max", "value"),
        Output("avg-winding-temp", "value"),
        Output("avg-winding-rise", "value"),
        Output("top-oil-rise", "value"),
        Output("ptot-used", "value"),
        Output("tau0-result", "value"),
        Output("temp-rise-error-message", "children")
    ],
    [
        Input("url", "pathname"),                    # <<< Trigger pela URL
        Input("transformer-inputs-store", "data"),   # <<< Trigger pelos Dados Básicos
        Input("temperature-rise-store", "data"),     # <<< Trigger pelo store local
        Input("losses-store", "data")                # <<< Trigger pelos dados de perdas
    ],
    prevent_initial_call=False # <<< Permite rodar na carga inicial
)
def temperature_rise_load_data(pathname, transformer_data, stored_temp_rise_data, losses_data):
    """
    Carrega os dados da aba Elevação de Temperatura para a UI.
    - Prioriza 'elevacao_oleo_topo' do transformer-inputs-store.
    - Carrega outros inputs e resultados do temperature-rise-store.
    """
    triggered_id = callback_context.triggered_id
    log.info(f"[LOAD TempRise] Callback triggered by: {triggered_id}")
    log.info(f"[LOAD TempRise] Store data: {stored_temp_rise_data}")
    log.info(f"[LOAD TempRise] Store type: {type(stored_temp_rise_data)}")
    if isinstance(stored_temp_rise_data, dict):
        log.info(f"[LOAD TempRise] Store length: {len(stored_temp_rise_data)}")
        log.info(f"[LOAD TempRise] Store keys: {list(stored_temp_rise_data.keys())}")

    normalized_path = normalize_pathname(pathname)
    # Só executa a lógica principal se o trigger for a URL E estiver na página correta,
    # OU se o trigger for a atualização do store global (para pegar delta_theta)
    # OU se o trigger for o store local (para carregar dados salvos)
    # OU se o trigger for o store de perdas (para atualizar cálculos)
    should_process = False

    # Caso 1: Estamos na página correta (independente do trigger)
    if normalized_path == ROUTE_TEMPERATURE_RISE:
        should_process = True
        log.info(f"[LOAD TempRise] Estamos na página de Elevação de Temperatura. Processando.")
    # Caso 2: Trigger é o store global (para pegar delta_theta)
    elif triggered_id == "transformer-inputs-store":
        should_process = True
        log.info(f"[LOAD TempRise] Processando atualização de transformer-inputs-store.")
    # Caso 3: Trigger é o store de perdas
    elif triggered_id == "losses-store":
        should_process = True
        log.info(f"[LOAD TempRise] Processando atualização de losses-store.")
    # Caso 4: Estamos em outra página e o trigger é a URL
    elif triggered_id == "url" and normalized_path != ROUTE_TEMPERATURE_RISE:
         log.debug(f"[LOAD TempRise] Pathname '{pathname}' não é '{ROUTE_TEMPERATURE_RISE}'. Abortando trigger de URL.")
         raise PreventUpdate
    # Caso 5: Trigger é o store local (para carregar dados salvos ou limpeza)
    elif triggered_id == "temperature-rise-store":
        should_process = True
        log.info(f"[LOAD TempRise] Processando atualização de temperature-rise-store. Dados: {stored_temp_rise_data}")
    # Caso 6: Outros triggers não relevantes
    else:
        log.debug(f"[LOAD TempRise] Trigger não relevante ({triggered_id}). Abortando.")
        raise PreventUpdate

    if not should_process: # Segurança extra
        raise PreventUpdate

    # Lê dados dos stores, tratando None/inválido
    local_data = stored_temp_rise_data if stored_temp_rise_data and isinstance(stored_temp_rise_data, dict) else {}
    global_data = transformer_data if transformer_data and isinstance(transformer_data, dict) else {}
    losses_data = losses_data if losses_data and isinstance(losses_data, dict) else {}

    # Verificar se foi uma limpeza intencional do store
    if triggered_id == "temperature-rise-store" and isinstance(stored_temp_rise_data, dict) and len(stored_temp_rise_data) == 0:
        log.info("[LOAD TempRise] Store foi limpo. Limpando todos os inputs visuais.")
        # Retornar valores vazios/None para limpar os inputs, exceto delta_theta_oil_max que vem do global
        delta_theta_oil_max_global = None
        if global_data and "transformer_data" in global_data:
            delta_theta_oil_max_global = global_data["transformer_data"].get('elevacao_oleo_topo')
        elif global_data:
            delta_theta_oil_max_global = global_data.get('elevacao_oleo_topo')

        return (
            None,  # temp_amb
            'cobre',  # material (manter padrão)
            None,  # res_cold
            None,  # temp_cold
            None,  # res_hot
            None,  # temp_top_oil
            delta_theta_oil_max_global,  # delta_theta_oil_max (do global se disponível)
            None,  # avg_winding_temp
            None,  # avg_winding_rise
            None,  # top_oil_rise
            None,  # ptot_used
            None,  # tau0_result
            ""     # error_message
        )

    # Verificar se os dados do transformador estão aninhados em transformer_data
    if "transformer_data" in global_data and isinstance(global_data["transformer_data"], dict):
        # Usar os dados aninhados
        transformer_dict = global_data["transformer_data"]
        log.debug(f"[LOAD TempRise] Usando dados aninhados em transformer_data")
    else:
        # Usar os dados diretamente
        transformer_dict = global_data
        log.debug(f"[LOAD TempRise] Usando dados diretamente do dicionário principal")

    inputs_local = local_data.get('inputs_temp_rise', {})
    results_local = local_data.get('resultados_temp_rise', {})

    # Verificar se os dados estão diretamente no dicionário principal
    log.debug(f"[LOAD TempRise] Verificando dados diretamente no dicionário principal: {list(local_data.keys())}")

    # Verificar inputs diretamente no dicionário principal
    if "input_ta" in local_data and not inputs_local.get("input_ta"):
        inputs_local["input_ta"] = local_data.get("input_ta")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_ta={inputs_local['input_ta']}")

    if "input_material" in local_data and not inputs_local.get("input_material"):
        inputs_local["input_material"] = local_data.get("input_material")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_material={inputs_local['input_material']}")

    if "input_rc" in local_data and not inputs_local.get("input_rc"):
        inputs_local["input_rc"] = local_data.get("input_rc")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_rc={inputs_local['input_rc']}")

    if "input_tc" in local_data and not inputs_local.get("input_tc"):
        inputs_local["input_tc"] = local_data.get("input_tc")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_tc={inputs_local['input_tc']}")

    if "input_rw" in local_data and not inputs_local.get("input_rw"):
        inputs_local["input_rw"] = local_data.get("input_rw")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_rw={inputs_local['input_rw']}")

    if "input_t_oil" in local_data and not inputs_local.get("input_t_oil"):
        inputs_local["input_t_oil"] = local_data.get("input_t_oil")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_t_oil={inputs_local['input_t_oil']}")

    if "input_delta_theta_oil_max" in local_data and not inputs_local.get("input_delta_theta_oil_max"):
        inputs_local["input_delta_theta_oil_max"] = local_data.get("input_delta_theta_oil_max")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: input_delta_theta_oil_max={inputs_local['input_delta_theta_oil_max']}")

    # Verificar resultados diretamente no dicionário principal
    if "avg_winding_temp" in local_data and not results_local.get("avg_winding_temp"):
        results_local["avg_winding_temp"] = local_data.get("avg_winding_temp")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: avg_winding_temp={results_local['avg_winding_temp']}")

    if "avg_winding_rise" in local_data and not results_local.get("avg_winding_rise"):
        results_local["avg_winding_rise"] = local_data.get("avg_winding_rise")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: avg_winding_rise={results_local['avg_winding_rise']}")

    if "top_oil_rise" in local_data and not results_local.get("top_oil_rise"):
        results_local["top_oil_rise"] = local_data.get("top_oil_rise")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: top_oil_rise={results_local['top_oil_rise']}")

    if "ptot_used_kw" in local_data and not results_local.get("ptot_used_kw"):
        results_local["ptot_used_kw"] = local_data.get("ptot_used_kw")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: ptot_used_kw={results_local['ptot_used_kw']}")

    if "tau0_h" in local_data and not results_local.get("tau0_h"):
        results_local["tau0_h"] = local_data.get("tau0_h")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: tau0_h={results_local['tau0_h']}")

    if "message" in local_data and not results_local.get("message"):
        results_local["message"] = local_data.get("message")
        log.debug(f"[LOAD TempRise] Valor encontrado diretamente no dicionário principal: message={results_local['message']}")

    log.debug(f"[LOAD TempRise] Dados lidos do local store: Inputs={inputs_local}, Resultados={results_local}")
    log.debug(f"[LOAD TempRise] Dados lidos do global store: {global_data}")

    # Determina delta_theta_oil_max (prioridade global)
    delta_theta_oil_max_final = None
    delta_theta_oil_max_global_raw = transformer_dict.get('elevacao_oleo_topo')
    if delta_theta_oil_max_global_raw is not None:
        delta_theta_oil_max_final = safe_float(delta_theta_oil_max_global_raw)
        if delta_theta_oil_max_final is None:
             log.debug(f"[LOAD TempRise] Falha ao converter 'elevacao_oleo_topo' ({delta_theta_oil_max_global_raw}). Fallback para local.")
             delta_theta_oil_max_final = inputs_local.get('input_delta_theta_oil_max') # Já deve ser float
    else:
        delta_theta_oil_max_final = inputs_local.get('input_delta_theta_oil_max')

    # Se ainda for None, usar um valor padrão
    if delta_theta_oil_max_final is None:
        delta_theta_oil_max_final = 55.0  # Valor padrão para classe A
        log.debug(f"[LOAD TempRise] Usando valor padrão para delta_theta_oil_max: {delta_theta_oil_max_final}")

    # Tenta carregar a mensagem salva
    message_str = results_local.get('message', "")
    display_message = ""
    if message_str:
         is_warning = "Aviso" in message_str
         display_message = html.Div(message_str,
                                    style={"color": colors.get('warning', 'orange') if is_warning else colors.get('fail', 'red'),
                                           "fontSize": "0.7rem"})

    # Retorna valores para UI
    values_to_return = (
        inputs_local.get('input_ta'), inputs_local.get('input_material', 'cobre'),
        inputs_local.get('input_rc'), inputs_local.get('input_tc'),
        inputs_local.get('input_rw'), inputs_local.get('input_t_oil'),
        delta_theta_oil_max_final, # Valor numérico
        results_local.get('avg_winding_temp'), results_local.get('avg_winding_rise'),
        results_local.get('top_oil_rise'), results_local.get('ptot_used_kw'),
        results_local.get('tau0_h'), display_message
    )

    log.debug(f"[LOAD TempRise] Valores finais retornados para UI: {values_to_return}")
    return values_to_return


# Callback simples para mostrar quando o botão é pressionado
@app.callback(
    Output("temp-rise-button-log", "children"),
    [Input("calc-temp-rise-btn", "n_clicks")],
    prevent_initial_call=True
)
def log_button_press(n_clicks):
    """Callback simples para mostrar quando o botão Calcular Elevação é pressionado."""
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")

    print(f"\n🔥🔥🔥 [BOTÃO PRESSIONADO] Calcular Elevação - Clique #{n_clicks} às {timestamp} 🔥🔥🔥")
    log.info(f"🔥 BOTÃO CALCULAR ELEVAÇÃO PRESSIONADO! Clique #{n_clicks} às {timestamp}")

    return html.Div(
        f"✅ Botão pressionado {n_clicks} vezes (último: {timestamp})",
        style={"color": "green", "fontSize": "0.8rem", "fontWeight": "bold"}
    )

# Callback de debug para o botão alternativo
@app.callback(
    Output("temp-rise-error-message", "children", allow_duplicate=True),
    [Input("calc-temp-rise-debug", "n_clicks")],
    prevent_initial_call=True
)
def debug_calc_button(n_clicks):
    log.info(f"***** [DEBUG] Botão TESTE Calcular clicado: n_clicks = {n_clicks} *****")
    print(f"\n\n***** [DEBUG] Botão TESTE Calcular clicado: n_clicks = {n_clicks} *****\n\n")

    # Criar um arquivo de log específico para debug
    with open('logs/debug_calcular_button.log', 'a') as f:
        f.write(f"\n\n***** [DEBUG] Botão TESTE Calcular clicado *****\n")
        f.write(f"[DEBUG] n_clicks = {n_clicks}\n")
        f.write(f"[DEBUG] Trigger: {callback_context.triggered_id}\n")
        f.write(f"[DEBUG] Triggered: {callback_context.triggered}\n")
        f.write(f"[DEBUG] Timestamp: {datetime.datetime.now().isoformat()}\n")

    return html.Div(f"Botão TESTE clicado: {n_clicks} vezes", style={"color": "red", "fontSize": "0.8rem"})

# Callback principal para cálculo (CORRIGIDO)
@app.callback(
    [
        Output("avg-winding-temp", "value", allow_duplicate=True),
        Output("avg-winding-rise", "value", allow_duplicate=True),
        Output("top-oil-rise", "value", allow_duplicate=True),
        Output("ptot-used", "value", allow_duplicate=True),
        Output("tau0-result", "value", allow_duplicate=True),
        Output("temp-rise-error-message", "children", allow_duplicate=True),
        Output("temperature-rise-store", "data", allow_duplicate=True)
    ],
    [
        Input("calc-temp-rise-btn", "n_clicks"),  # Trigger: Botão Calcular
    ],
    [
        State("temp-amb", "value"), State("winding-material", "value"),
        State("res-cold", "value"), State("temp-cold", "value"),
        State("res-hot", "value"), State("temp-top-oil", "value"),
        State("delta-theta-oil-max", "value"),
        State("transformer-inputs-store", "data"),
        State("losses-store", "data"),
        State("temperature-rise-store", "data"),
        State("url", "pathname") # Adicionado pathname para verificação
    ],
    prevent_initial_call=True
)
def temperature_rise_calculate(
    calc_clicks, temp_amb_str, winding_material, res_cold_str, temp_cold_str, res_hot_str, temp_top_oil_str, delta_theta_oil_max_str, # Inputs locais
    transformer_data, losses_data, current_store_data, pathname): # Dados globais e pathname
    """
    Calcula elevação de temperatura, Ptot usada e τ₀.
    Busca dados globais, realiza cálculos e salva inputs/resultados no store local.
    """
    # LOGS DETALHADOS DE ENTRADA
    print(f"\n{'='*80}")
    print(f"[DEBUG CALC ELEVAÇÃO] CALLBACK INICIADO!")
    print(f"[DEBUG CALC ELEVAÇÃO] Cliques: {calc_clicks}")
    print(f"[DEBUG CALC ELEVAÇÃO] Triggered ID: {callback_context.triggered_id}")
    print(f"[DEBUG CALC ELEVAÇÃO] Triggered: {callback_context.triggered}")
    print(f"[DEBUG CALC ELEVAÇÃO] Pathname: {pathname}")
    print(f"[DEBUG CALC ELEVAÇÃO] Inputs recebidos:")
    print(f"  - temp_amb_str: {temp_amb_str}")
    print(f"  - winding_material: {winding_material}")
    print(f"  - res_cold_str: {res_cold_str}")
    print(f"  - temp_cold_str: {temp_cold_str}")
    print(f"  - res_hot_str: {res_hot_str}")
    print(f"  - temp_top_oil_str: {temp_top_oil_str}")
    print(f"  - delta_theta_oil_max_str: {delta_theta_oil_max_str}")
    print(f"{'='*80}\n")

    log.info(f"🔘 [TEMP RISE] BOTÃO 'CALCULAR ELEVAÇÃO' PRESSIONADO!")
    log.info(f"   🔢 Cliques: {calc_clicks}")
    log.info(f"   🎯 Trigger: {callback_context.triggered_id}")
    log.info(f"   📍 Pathname: {pathname}")

    # Verificar se estamos na página correta
    normalized_path = normalize_pathname(pathname)
    if normalized_path != ROUTE_TEMPERATURE_RISE:
        print(f"[DEBUG CALC ELEVAÇÃO] ❌ Página incorreta: {normalized_path} != {ROUTE_TEMPERATURE_RISE}")
        log.info(f"Ignorando callback temperature_rise_calculate em página diferente: {pathname}")
        raise PreventUpdate

    # Identificar qual botão foi clicado
    triggered_id = callback_context.triggered_id
    print(f"[DEBUG CALC ELEVAÇÃO] Verificando trigger: {triggered_id}")

    if triggered_id == "calc-temp-rise-btn" and (calc_clicks is None or calc_clicks <= 0):
        print(f"[DEBUG CALC ELEVAÇÃO] ❌ Cliques inválidos: {calc_clicks}")
        raise PreventUpdate

    # Se nenhum botão foi clicado (improvável, mas por segurança)
    if triggered_id != "calc-temp-rise-btn":
        print(f"[DEBUG CALC ELEVAÇÃO] ❌ Trigger inesperado: {triggered_id}")
        log.warning(f"[CALC TempRise] Trigger inesperado: {triggered_id}")
        raise PreventUpdate

    print(f"[DEBUG CALC ELEVAÇÃO] ✅ Validações passaram! Iniciando cálculos...")
    log.info(f"   📊 Parâmetros: Tamb={temp_amb_str}°C, Material={winding_material}, Rfria={res_cold_str}Ω")
    log.info(f"   📊 Resistências: Tfria={temp_cold_str}°C, Rquente={res_hot_str}Ω, Tóleo={temp_top_oil_str}°C")

    # --- 1. Validar e converter inputs locais ---
    input_values_local = {
        'input_ta': safe_float(temp_amb_str, 25.0),
        'input_material': winding_material or 'cobre',
        'input_rc': safe_float(res_cold_str),
        'input_tc': safe_float(temp_cold_str),
        'input_rw': safe_float(res_hot_str),
        'input_t_oil': safe_float(temp_top_oil_str),
        'input_delta_theta_oil_max': safe_float(delta_theta_oil_max_str, 55.0)
    }

    # --- 2. Obter dados do transformador ---
    # Verificar se os dados do transformador estão aninhados em transformer_data
    transformer_dict = {}
    if transformer_data and isinstance(transformer_data, dict):
        if "transformer_data" in transformer_data and isinstance(transformer_data["transformer_data"], dict):
            # Usar os dados aninhados
            transformer_dict = transformer_data["transformer_data"]
            log.debug(f"[CALC TempRise] Usando dados aninhados em transformer_data")
        else:
            # Usar os dados diretamente
            transformer_dict = transformer_data
            log.debug(f"[CALC TempRise] Usando dados diretamente do dicionário principal")

    # --- 3. Obter dados de perdas ---
    losses_dict = {}
    if losses_data and isinstance(losses_data, dict):
        if "resultados_perdas_vazio" in losses_data and isinstance(losses_data["resultados_perdas_vazio"], dict):
            # Usar os dados aninhados
            losses_dict = losses_data
            log.debug(f"[CALC TempRise] Usando dados de perdas aninhados")
        else:
            # Usar os dados diretamente
            losses_dict = losses_data
            log.debug(f"[CALC TempRise] Usando dados de perdas diretamente do dicionário principal")

    # --- 4. Extrair valores necessários ---
    # Dados do transformador
    potencia_mva = safe_float(transformer_dict.get('potencia_mva'))
    peso_nucleo_kg = safe_float(transformer_dict.get('peso_nucleo_kg'))
    peso_oleo_kg = safe_float(transformer_dict.get('peso_oleo_kg'))
    peso_tanque_kg = safe_float(transformer_dict.get('peso_tanque_kg'))
    peso_enrol_at_kg = safe_float(transformer_dict.get('peso_enrol_at_kg'))
    peso_enrol_bt_kg = safe_float(transformer_dict.get('peso_enrol_bt_kg'))
    peso_enrol_ter_kg = safe_float(transformer_dict.get('peso_enrol_ter_kg', 0))  # Terciário pode não existir

    # Dados de perdas
    perdas_vazio_kw = None
    perdas_totais_kw = None

    # Tentar obter perdas do dicionário de resultados
    if "resultados_perdas_vazio" in losses_dict:
        perdas_vazio_kw = safe_float(losses_dict["resultados_perdas_vazio"].get("perdas_vazio_kw"))

    # Se não encontrou, tentar diretamente no dicionário principal
    if perdas_vazio_kw is None:
        perdas_vazio_kw = safe_float(losses_dict.get("perdas_vazio_kw"))

    # Tentar obter perdas totais do dicionário de resultados
    if "resultados_perdas_totais" in losses_dict:
        perdas_totais_kw = safe_float(losses_dict["resultados_perdas_totais"].get("perdas_totais_kw"))

    # Se não encontrou, tentar diretamente no dicionário principal
    if perdas_totais_kw is None:
        perdas_totais_kw = safe_float(losses_dict.get("perdas_totais_kw"))

    # Se ainda não encontrou, tentar calcular a partir de perdas em carga + perdas em vazio
    if perdas_totais_kw is None:
        perdas_carga_kw = None
        if "resultados_perdas_carga" in losses_dict:
            perdas_carga_kw = safe_float(losses_dict["resultados_perdas_carga"].get("perdas_carga_kw"))
        if perdas_carga_kw is None:
            perdas_carga_kw = safe_float(losses_dict.get("perdas_carga_kw"))

        if perdas_carga_kw is not None and perdas_vazio_kw is not None:
            perdas_totais_kw = perdas_carga_kw + perdas_vazio_kw

    # --- 5. Validar dados necessários ---
    missing_data = []
    if potencia_mva is None:
        missing_data.append("potência (MVA)")
    if perdas_vazio_kw is None:
        missing_data.append("perdas em vazio (kW)")
    if perdas_totais_kw is None:
        missing_data.append("perdas totais (kW)")

    # Verificar pesos (pelo menos um deve estar presente)
    pesos_presentes = [p for p in [peso_nucleo_kg, peso_oleo_kg, peso_tanque_kg,
                                  peso_enrol_at_kg, peso_enrol_bt_kg, peso_enrol_ter_kg]
                      if p is not None]
    if not pesos_presentes:
        missing_data.append("pesos dos componentes (kg)")

    # Verificar inputs locais necessários
    if input_values_local['input_rc'] is None:
        missing_data.append("resistência a frio (Ω)")
    if input_values_local['input_tc'] is None:
        missing_data.append("temperatura a frio (°C)")
    if input_values_local['input_rw'] is None:
        missing_data.append("resistência a quente (Ω)")

    # Se faltam dados, exibir mensagem de erro
    if missing_data:
        error_msg = f"Dados necessários ausentes: {', '.join(missing_data)}."

        print(f"\n[DEBUG CALC ELEVAÇÃO] ❌ ERRO - DADOS FALTANTES!")
        print(f"[DEBUG CALC ELEVAÇÃO] Dados ausentes: {missing_data}")
        print(f"[DEBUG CALC ELEVAÇÃO] Mensagem: {error_msg}")
        print(f"{'='*80}\n")

        log.warning(f"[CALC TempRise] {error_msg}")

        # Preparar mensagem para UI
        display_message = html.Div(error_msg, style={"color": colors.get('fail', 'red'), "fontSize": "0.7rem"})

        # Atualizar o store com os inputs, mas sem resultados
        new_store_data = current_store_data.copy() if current_store_data else {}
        new_store_data['inputs_temp_rise'] = input_values_local
        new_store_data['message'] = error_msg

        return no_update, no_update, no_update, no_update, no_update, display_message, new_store_data

    # --- 6. Realizar cálculos ---
    try:
        log.info(f"[CALC TempRise] Iniciando cálculos com dados: P={potencia_mva}MVA, P0={perdas_vazio_kw}kW, Ptot={perdas_totais_kw}kW")

        # Calcular temperatura média do enrolamento
        avg_winding_temp, avg_winding_rise = calculate_winding_temps(
            input_values_local['input_rc'],
            input_values_local['input_tc'],
            input_values_local['input_rw'],
            input_values_local['input_ta'],
            input_values_local['input_material']
        )

        # Calcular elevação de temperatura do óleo
        top_oil_rise = calculate_top_oil_rise(
            perdas_totais_kw,
            input_values_local['input_delta_theta_oil_max']
        )

        # Calcular constante de tempo térmica
        # Somar pesos disponíveis
        peso_total_kg = sum(p for p in [peso_nucleo_kg, peso_oleo_kg, peso_tanque_kg,
                                       peso_enrol_at_kg, peso_enrol_bt_kg, peso_enrol_ter_kg]
                           if p is not None)

        tau0_h = calculate_thermal_time_constant(
            peso_total_kg,
            perdas_totais_kw
        )

        # --- 7. Preparar resultados ---
        results = {
            'avg_winding_temp': avg_winding_temp,
            'avg_winding_rise': avg_winding_rise,
            'top_oil_rise': top_oil_rise,
            'ptot_used_kw': perdas_totais_kw,
            'tau0_h': tau0_h
        }

        # Formatar para exibição
        display_avg_winding_temp = f"{avg_winding_temp:.1f}" if avg_winding_temp is not None else ""
        display_avg_winding_rise = f"{avg_winding_rise:.1f}" if avg_winding_rise is not None else ""
        display_top_oil_rise = f"{top_oil_rise:.1f}" if top_oil_rise is not None else ""
        display_ptot_used = f"{perdas_totais_kw:.1f}" if perdas_totais_kw is not None else ""
        display_tau0 = f"{tau0_h:.1f}" if tau0_h is not None else ""

        # --- 8. Atualizar o store ---
        new_store_data = current_store_data.copy() if current_store_data else {}
        new_store_data['inputs_temp_rise'] = input_values_local
        new_store_data['resultados_temp_rise'] = results
        new_store_data['timestamp'] = datetime.datetime.now().isoformat()

        # Remover mensagem de erro se existir
        if 'message' in new_store_data:
            del new_store_data['message']

        print(f"\n[DEBUG CALC ELEVAÇÃO] ✅ CÁLCULOS CONCLUÍDOS COM SUCESSO!")
        print(f"[DEBUG CALC ELEVAÇÃO] Resultados:")
        print(f"  - Temp. Média Enrolamento: {avg_winding_temp:.1f}°C")
        print(f"  - Elevação Média Enrolamento: {avg_winding_rise:.1f}°C")
        print(f"  - Elevação Topo Óleo: {top_oil_rise:.1f}°C")
        print(f"  - Perdas Totais Usadas: {perdas_totais_kw:.1f}kW")
        print(f"  - Constante Tempo Térmica: {tau0_h:.1f}h")
        print(f"[DEBUG CALC ELEVAÇÃO] Store atualizado com sucesso!")
        print(f"{'='*80}\n")

        log.info(f"[CALC TempRise] Cálculos concluídos: Tw={avg_winding_temp:.1f}°C, ΔTw={avg_winding_rise:.1f}°C, ΔToil={top_oil_rise:.1f}°C, τ0={tau0_h:.1f}h")

        return (
            display_avg_winding_temp,
            display_avg_winding_rise,
            display_top_oil_rise,
            display_ptot_used,
            display_tau0,
            "",  # Sem mensagem de erro
            new_store_data
        )

    except Exception as e:
        print(f"\n[DEBUG CALC ELEVAÇÃO] ❌ EXCEÇÃO DURANTE CÁLCULOS!")
        print(f"[DEBUG CALC ELEVAÇÃO] Erro: {str(e)}")
        print(f"[DEBUG CALC ELEVAÇÃO] Tipo: {type(e).__name__}")
        print(f"{'='*80}\n")

        log.exception(f"[CALC TempRise] Erro nos cálculos: {e}")
        error_msg = f"Erro nos cálculos: {str(e)}"
        display_message = html.Div(error_msg, style={"color": colors.get('fail', 'red'), "fontSize": "0.7rem"})

        # Atualizar o store com os inputs, mas sem resultados
        new_store_data = current_store_data.copy() if current_store_data else {}
        new_store_data['inputs_temp_rise'] = input_values_local
        new_store_data['message'] = error_msg

        return no_update, no_update, no_update, no_update, no_update, display_message, new_store_data


def register_temperature_rise_callbacks(app_instance):
    """
    Função de registro de callbacks para o módulo temperature_rise.
    Esta função é chamada pelo app.py para registrar os callbacks.

    Args:
        app_instance: Instância da aplicação Dash
    """
    log.info(f"Registrando callbacks do módulo temperature_rise para app {app_instance.title}...")

    # Os callbacks já estão registrados via decoradores @app.callback
    # Esta função existe apenas para manter compatibilidade com o sistema de registro

    log.info("Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.")
    return True