# layouts/induced_voltage.py
"""
Defines the layout for the Induced Voltage section as a function.
"""
# Importações para obter dados do transformador
import logging

import dash_bootstrap_components as dbc
from dash import dcc, html

from components.help_button import create_help_button

# Import reusable components
from components.ui_elements import create_labeled_input

log = logging.getLogger(__name__)

# Importar estilos padronizados
from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY


# --- Layout Definition Function ---
def create_induced_voltage_layout():
    """Creates the layout component for the Induced Voltage section."""
    log.info("[Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback")
    return dbc.Container(
        [
            dbc.Row(
                [
                    dbc.Col(
                        [
                            html.Div(
                                [
                                    html.Div(id="transformer-info-induced-page", className="mb-2"),
                                    html.Div(html.Div(), id="transformer-info-induced", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-losses", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-impulse", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-dieletric", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-applied", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-short-circuit", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-temperature-rise", style={"display": "none"}),
                                    html.Div(html.Div(), id="transformer-info-comprehensive", style={"display": "none"}),
                                ]
                            )
                        ],
                        width=12,
                    )
                ],
                className=SPACING["row_margin"],
            ),
            dbc.Card(
                [
                    dbc.CardHeader(
                        [
                            html.Span("Tensão Induzida", style={"fontWeight": "bold", "fontSize": "1.1rem", "color": COLORS["text_dark"]}),
                            create_help_button("induced-voltage-help"),
                        ],
                        style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}
                    ),
                    dbc.CardBody(
                        [
                            create_labeled_input("Tempo de Ensaio (s)", "induced-time", "Ex: 60", persistence=True, persistence_type="session"),
                            create_labeled_input("Tensão de Ensaio (V)", "induced-voltage", "Ex: 2000", persistence=True, persistence_type="session"),
                            create_labeled_input("Frequência (Hz)", "induced-frequency", "Ex: 120", persistence=True, persistence_type="session"),
                            html.Div(id="induced-corrente-info", style={"fontSize": "0.8rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
                            html.Div(id="induced-voltage-result", style={"fontWeight": "bold", "fontSize": "0.9rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
                        ],
                        style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}
                    ),
                ],
                style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "boxShadow": "0 1px 3px rgba(0,0,0,0.07)", "border": f"1px solid {COLORS['border']}"}
            ),
        ],
        fluid=True,
        style={"backgroundColor": COLORS["background_light"], "color": COLORS["text_dark"]}
    )
