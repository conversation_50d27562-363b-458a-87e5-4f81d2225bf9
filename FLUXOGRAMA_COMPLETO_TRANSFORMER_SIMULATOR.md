# FLUXOGRAMA COMPLETO - SIMULADOR DE TRANSFORMADORES

## 🏗️ ARQUITETURA GERAL

```mermaid
graph TB
    subgraph "INICIALIZAÇÃO"
        A[app.py] --> B[Carregar config.py]
        B --> C[Configurar Logging]
        C --> D[Verificar Limite de Uso]
        D --> E[Inicializar Dash App]
        E --> F[Inicializar StateManager]
        F --> G[C<PERSON>r Layout Principal]
        G --> H[Registrar Callbacks]
        H --> I[Iniciar Servidor]
    end

    subgraph "CORE SYSTEM"
        F --> SM[StateManager]
        SM --> MCP[TransformerMCP]
        MCP --> DISK[Persistência em Disco]
        MCP --> STORES[Global Stores]
    end
```

## 🔄 FLUXO DE DADOS PRINCIPAL

```mermaid
graph LR
    subgraph "ENTRADA DE DADOS"
        UI[Interface do Usuário] --> CB[Callbacks]
        CB --> SM[StateManager]
    end

    subgraph "PROCESSAMENTO"
        SM --> MCP[TransformerMCP]
        MCP --> CALC[Cálculos/Formulas]
        CALC --> VALID[Validação]
    end

    subgraph "PERSISTÊNCIA"
        MCP --> DISK[Disco JSON]
        MCP --> SESSION[Session Storage]
        DISK --> BACKUP[Backups]
    end

    subgraph "SAÍDA"
        VALID --> DISPLAY[Exibição UI]
        VALID --> PDF[Relatório PDF]
        VALID --> HIST[Histórico]
    end
```

## 🏛️ ESTRUTURA DE MÓDULOS

```mermaid
graph TB
    subgraph "LAYOUTS"
        ML[main_layout.py] --> NAV[Navegação]
        ML --> CONTENT[Área de Conteúdo]

        TI[transformer_inputs.py]
        LOSS[losses.py]
        IMP[impulse.py]
        DA[dieletric_analysis.py]
        AV[applied_voltage.py]
        IV[induced_voltage.py]
        SC[short_circuit.py]
        TR[temperature_rise.py]
        HIST[history.py]
        STD[standards.py]
    end

    subgraph "CALLBACKS"
        TIC[transformer_inputs callbacks]
        LOSSC[losses callbacks]
        IMPC[impulse callbacks]
        DAC[dieletric callbacks]
        AVC[applied voltage callbacks]
        IVC[induced voltage callbacks]
        SCC[short circuit callbacks]
        TRC[temperature rise callbacks]
        HISTC[history callbacks]
        STDC[standards callbacks]

        GA[global_actions.py]
        GU[global_updates.py]
        NAV_CB[navigation_dcc_links.py]
    end
```

## 🗄️ SISTEMA DE STORES

```mermaid
graph TB
    subgraph "GLOBAL STORES"
        GS[global_stores.py] --> TIS[transformer-inputs-store]
        GS --> LS[losses-store]
        GS --> IS[impulse-store]
        GS --> DAS[dieletric-analysis-store]
        GS --> AVS[applied-voltage-store]
        GS --> IVS[induced-voltage-store]
        GS --> SCS[short-circuit-store]
        GS --> TRS[temperature-rise-store]
        GS --> CAS[comprehensive-analysis-store]

        subgraph "STORES TEMPORÁRIOS"
            HTS[history-temp-store]
            DSS[delete-session-id-store]
            FRD[front-resistor-data]
            TRD[tail-resistor-data]
            CI[calculated-inductance]
            SS[simulation-status]
        end
    end

    subgraph "PERSISTÊNCIA"
        TIS --> MCP[TransformerMCP]
        LS --> MCP
        IS --> MCP
        DAS --> MCP
        AVS --> MCP
        IVS --> MCP
        SCS --> MCP
        TRS --> MCP
        CAS --> MCP
    end
```

## ⚙️ FLUXO DE CALLBACKS

```mermaid
graph TB
    subgraph "TIPOS DE CALLBACKS"
        subgraph "DECORADOS (Auto-registro)"
            DC1[navigation_dcc_links]
            DC2[losses]
            DC3[dieletric_analysis]
            DC4[report_generation]
            DC5[dielectric_analysis_comprehensive]
            DC6[global_updates]
            DC7[standards_consultation]
            DC8[standards_management]
            DC9[logging_controls]
        end

        subgraph "EXPLÍCITOS (Registro manual)"
            EC1[insulation_level_callbacks]
            EC2[transformer_inputs]
            EC3[short_circuit]
            EC4[impulse]
            EC5[applied_voltage]
            EC6[induced_voltage]
            EC7[history]
            EC8[global_actions]
            EC9[temperature_rise]
        end
    end

    subgraph "REGISTRO"
        APP[app.py] --> IMPORT[Importar Módulos]
        IMPORT --> REG[Registrar Callbacks]
        REG --> DASH[Dash App]
    end
```

## 🔄 CICLO DE VIDA DOS DADOS

```mermaid
sequenceDiagram
    participant U as Usuário
    participant UI as Interface
    participant CB as Callback
    participant SM as StateManager
    participant MCP as TransformerMCP
    participant DISK as Disco

    U->>UI: Insere dados
    UI->>CB: Trigger callback
    CB->>SM: set_store()
    SM->>MCP: set_data()
    MCP->>MCP: Validar/Converter
    MCP->>DISK: Salvar (auto)
    MCP->>CB: Retornar dados
    CB->>UI: Atualizar interface
    UI->>U: Mostrar resultado
```

## 🧮 SISTEMA DE CÁLCULOS

```mermaid
graph TB
    subgraph "FORMULAS"
        EM[electrical_math.py]
        IM[impulse_math.py]
        LM[losses_math.py]
        TM[thermal_math.py]
        TRM[transformer_math.py]
        UTILS[utils.py]
    end

    subgraph "APLICAÇÃO"
        CALLBACKS --> EM
        CALLBACKS --> IM
        CALLBACKS --> LM
        CALLBACKS --> TM
        CALLBACKS --> TRM
        CALLBACKS --> UTILS
    end

    subgraph "VALIDAÇÃO"
        VALIDATORS[validators.py] --> RESULTS[Resultados]
        FORMATTERS[formatters.py] --> DISPLAY[Exibição]
    end
```

## 🔧 UTILITÁRIOS E SUPORTE

```mermaid
graph TB
    subgraph "PERSISTÊNCIA"
        MCP_DISK[mcp_disk_persistence.py]
        MCP_PERS[mcp_persistence.py]
        USAGE[usage_tracker.py]
        PATHS[paths.py]
    end

    subgraph "INTERFACE"
        UI_ELEM[ui_elements.py]
        HELP[help_button.py]
        TEMPLATE[transformer_info_template.py]
        STYLES[styles.py]
        COLORS[theme_colors.py]
    end

    subgraph "DADOS"
        DB_MGR[db_manager.py]
        STD_DB[standards_db.py]
        HIST_DB[history database]
    end

    subgraph "RELATÓRIOS"
        PDF_GEN[pdf_generator.py]
        REPORT[report_generation.py]
    end
```

## 🌐 FLUXO DE NAVEGAÇÃO

```mermaid
graph LR
    subgraph "ROTAS"
        HOME[/] --> DADOS[/dados]
        HOME --> PERDAS[/perdas]
        HOME --> IMPULSO[/impulso]
        HOME --> DIELETRICA[/analise-dieletrica]
        HOME --> COMPLETA[/analise-dieletrica-completa]
        HOME --> APLICADA[/tensao-aplicada]
        HOME --> INDUZIDA[/tensao-induzida]
        HOME --> CURTO[/curto-circuito]
        HOME --> TEMP[/elevacao-temperatura]
        HOME --> HIST[/historico]
        HOME --> NORMAS[/consulta-normas]
        HOME --> GERENCIAR[/gerenciar-normas]
    end

    subgraph "CALLBACK DE NAVEGAÇÃO"
        URL[dcc.Location] --> NAV_CB[navigation_dcc_links.py]
        NAV_CB --> CONTENT[Renderizar Conteúdo]
        NAV_CB --> STORES[Carregar Stores]
    end
```

## 🔒 SISTEMA DE ISOLAMENTO

```mermaid
graph TB
    subgraph "ISOLAMENTO"
        ISO_REPO[isolation_repo.py]
        ISO_LOGIC[isolation_logic.py]
        ISO_PERS[insulation_persistence.py]
        ISO_CB[insulation_level_callbacks.py]
    end

    subgraph "DADOS DE ISOLAMENTO"
        NBI[Nível Básico de Impulso]
        SIL[Nível de Isolamento]
        VOLTAGE[Tensões de Teste]
        STANDARDS[Normas Aplicáveis]
    end

    subgraph "SINCRONIZAÇÃO"
        SYNC[sync_isolation_values_enhanced]
        PROP[ensure_mcp_data_propagation]
    end

    ISO_REPO --> NBI
    ISO_REPO --> SIL
    ISO_REPO --> VOLTAGE
    ISO_REPO --> STANDARDS

    ISO_CB --> SYNC
    SYNC --> PROP
    PROP --> MCP[TransformerMCP]
```

## 📊 FLUXO DE RELATÓRIOS

```mermaid
sequenceDiagram
    participant U as Usuário
    participant BTN as Botão PDF
    participant CB as Callback
    participant PDF as PDF Generator
    participant DATA as Dados MCP
    participant FILE as Arquivo

    U->>BTN: Clica "Gerar Relatório"
    BTN->>CB: Trigger callback
    CB->>DATA: Coletar dados
    DATA->>PDF: Processar dados
    PDF->>PDF: Gerar PDF
    PDF->>FILE: Salvar arquivo
    FILE->>U: Download PDF
```

## 🎨 SISTEMA DE TEMAS

```mermaid
graph TB
    subgraph "TEMAS"
        DARK[Tema Escuro]
        LIGHT[Tema Claro]
        TOGGLE[theme-toggle]
    end

    subgraph "CSS"
        BASE[base.css]
        CUSTOM[custom.css]
        COMPONENTS[components.css]
        UTILITIES[utilities.css]
        DARK_CSS[theme-dark.css]
        LIGHT_CSS[theme-light.css]
    end

    subgraph "JAVASCRIPT"
        SWITCHER[theme-switcher.js]
        SINGLE[single_instance.js]
    end

    TOGGLE --> SWITCHER
    SWITCHER --> DARK_CSS
    SWITCHER --> LIGHT_CSS
```

## 🔍 SISTEMA DE LOGS

```mermaid
graph TB
    subgraph "CONFIGURAÇÃO"
        CONFIG[config.py] --> LOG_SETUP[Configuração de Logs]
        LOG_SETUP --> FILE_HANDLER[Arquivo de Log]
        LOG_SETUP --> CONSOLE_HANDLER[Console Handler]
        LOG_SETUP --> FILTER[InputChangeFilter]
    end

    subgraph "CONTROLES"
        LOG_CTRL[logging_controls.py]
        LOG_LEVEL[Nível de Log]
        LOG_DISPLAY[Exibição de Logs]
    end

    subgraph "SAÍDA"
        LOG_FILE[app.log]
        CONSOLE[Console Output]
    end

    LOG_CTRL --> LOG_LEVEL
    LOG_LEVEL --> FILE_HANDLER
    LOG_LEVEL --> CONSOLE_HANDLER
    FILE_HANDLER --> LOG_FILE
    CONSOLE_HANDLER --> CONSOLE
```

## 📁 ESTRUTURA DE ARQUIVOS

```text
transformer_simulator_v2_complete/
├── 📄 app.py                          # Aplicação principal
├── ⚙️ config.py                       # Configurações
├── 📋 requirements.txt                # Dependências
├──
├── 🏗️ app_core/                       # Núcleo da aplicação
│   ├── state_manager.py               # Gerenciador de estado central
│   ├── transformer_mcp.py             # MCP (Model-Control-Persistence)
│   ├── isolation_repo.py              # Repositório de isolamento
│   ├── isolation_logic.py             # Lógica de isolamento
│   ├── calculations.py                # Cálculos centrais
│   └── standards.py                   # Normas técnicas
│
├── 🎨 layouts/                        # Layouts das páginas
│   ├── main_layout.py                 # Layout principal
│   ├── transformer_inputs.py          # Dados do transformador
│   ├── losses.py                      # Perdas
│   ├── impulse.py                     # Impulso
│   ├── dieletric_analysis.py          # Análise dielétrica
│   ├── applied_voltage.py             # Tensão aplicada
│   ├── induced_voltage.py             # Tensão induzida
│   ├── short_circuit.py               # Curto-circuito
│   ├── temperature_rise.py            # Elevação de temperatura
│   ├── history.py                     # Histórico
│   └── standards_*.py                 # Normas
│
├── 🔄 callbacks/                      # Callbacks do Dash
│   ├── navigation_dcc_links.py        # Navegação
│   ├── global_actions.py              # Ações globais
│   ├── global_updates.py              # Atualizações globais
│   ├── transformer_inputs.py          # Callbacks dados transformador
│   ├── losses.py                      # Callbacks perdas
│   ├── impulse.py                     # Callbacks impulso
│   ├── applied_voltage.py             # Callbacks tensão aplicada
│   ├── induced_voltage.py             # Callbacks tensão induzida
│   ├── short_circuit.py               # Callbacks curto-circuito
│   ├── temperature_rise.py            # Callbacks temperatura
│   ├── insulation_level_callbacks.py  # Callbacks isolamento
│   ├── history.py                     # Callbacks histórico
│   ├── report_generation.py           # Callbacks relatórios
│   └── standards_*.py                 # Callbacks normas
│
├── 🧩 components/                     # Componentes reutilizáveis
│   ├── global_stores.py               # Stores globais
│   ├── ui_elements.py                 # Elementos de UI
│   ├── transformer_info_template.py   # Template de informações
│   ├── help_button.py                 # Botão de ajuda
│   ├── formatters.py                  # Formatadores
│   └── validators.py                  # Validadores
│
├── 🧮 formulas/                       # Fórmulas matemáticas
│   ├── electrical_math.py             # Matemática elétrica
│   ├── impulse_math.py                # Matemática de impulso
│   ├── losses_math.py                 # Matemática de perdas
│   ├── thermal_math.py                # Matemática térmica
│   ├── transformer_math.py            # Matemática de transformadores
│   └── utils.py                       # Utilitários matemáticos
│
├── 🛠️ utils/                          # Utilitários
│   ├── mcp_disk_persistence.py        # Persistência em disco
│   ├── mcp_persistence.py             # Persistência MCP
│   ├── usage_tracker.py               # Rastreamento de uso
│   ├── db_manager.py                  # Gerenciador de BD
│   ├── standards_db.py                # BD de normas
│   ├── pdf_generator.py               # Gerador de PDF
│   ├── paths.py                       # Caminhos
│   ├── styles.py                      # Estilos
│   ├── theme_colors.py                # Cores dos temas
│   └── routes.py                      # Rotas
│
├── 🎨 assets/                         # Recursos estáticos
│   ├── custom.css                     # CSS personalizado
│   ├── theme-*.css                    # Temas
│   ├── theme-switcher.js              # Alternador de tema
│   ├── single_instance.js             # Instância única
│   └── DataLogo.jpg                   # Logo
│
├── 💾 data/                           # Dados persistentes
│   ├── mcp_state/                     # Estado do MCP
│   ├── history.db                     # BD histórico
│   ├── standards.db                   # BD normas
│   └── usage.db                       # BD uso
│
├── 📚 docs/                           # Documentação
│   ├── help_docs/                     # Documentos de ajuda
│   └── hacks/                         # Guias técnicos
│
└── 📊 logs/                           # Logs da aplicação
    └── app.log                        # Log principal
```

## 🔄 FLUXO COMPLETO DE EXECUÇÃO

```mermaid
sequenceDiagram
    participant USER as 👤 Usuário
    participant BROWSER as 🌐 Navegador
    participant APP as 📱 App.py
    participant SM as 🗄️ StateManager
    participant MCP as 💾 TransformerMCP
    participant CB as ⚙️ Callbacks
    participant UI as 🎨 Interface
    participant DISK as 💿 Disco

    USER->>BROWSER: Acessa aplicação
    BROWSER->>APP: Requisição HTTP
    APP->>APP: Inicializar configuração
    APP->>SM: Criar StateManager
    SM->>MCP: Inicializar MCP
    MCP->>DISK: Carregar dados salvos
    APP->>UI: Criar layout principal
    APP->>CB: Registrar callbacks
    APP->>BROWSER: Servir aplicação

    loop Interação do usuário
        USER->>UI: Inserir dados
        UI->>CB: Trigger callback
        CB->>SM: Atualizar estado
        SM->>MCP: Persistir dados
        MCP->>DISK: Salvar em disco
        CB->>UI: Retornar resultados
        UI->>USER: Exibir resultados
    end

    USER->>UI: Gerar relatório
    UI->>CB: Callback relatório
    CB->>MCP: Coletar dados
    CB->>CB: Gerar PDF
    CB->>USER: Download PDF
```

## 🎯 PONTOS-CHAVE DA ARQUITETURA

### ✅ **VANTAGENS**

- **Centralização**: StateManager como ponto único de controle
- **Persistência**: Dados salvos automaticamente em disco
- **Modularidade**: Cada módulo tem layout e callbacks separados
- **Flexibilidade**: Sistema de stores permite compartilhamento de dados
- **Robustez**: Sistema de backup e recuperação de dados
- **Escalabilidade**: Fácil adição de novos módulos

### ⚠️ **PONTOS DE ATENÇÃO**

- **Complexidade**: Múltiplas camadas de abstração
- **Dependências**: Forte acoplamento entre componentes
- **Performance**: Muitos callbacks podem impactar performance
- **Debugging**: Fluxo de dados complexo pode dificultar debug

### 🔧 **PADRÕES UTILIZADOS**

- **MVC**: Model (MCP) - View (Layouts) - Controller (Callbacks)
- **Repository**: isolation_repo.py para dados de isolamento
- **State Management**: StateManager centralizado
- **Observer**: Callbacks observam mudanças nos stores
- **Factory**: Criação dinâmica de componentes UI
