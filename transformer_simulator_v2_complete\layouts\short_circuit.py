# layouts/short_circuit.py
"""
Defines the layout for the Short-Circuit Withstand section as a function.
"""
import dash_bootstrap_components as dbc
from dash import dcc, html
import plotly.express as px # For the initial empty graph

# Import reusable components and constants
from components.ui_elements import create_labeled_input
from components.transformer_info_template import create_transformer_info_panel
from components.help_button import create_help_button
from utils import constants # For category options

# Importações para obter dados do transformador
from app import app # Para acessar o cache da aplicação
import logging
log = logging.getLogger(__name__)

# Importar estilos padronizados
from layouts import COLORS, TYPOGRAPHY, COMPONENTS, SPACING
from config import colors  # Importar cores para estilos de status

# Initial Empty Graph (created within the function now)
def create_empty_sc_figure():
    """ Creates an empty placeholder figure for the impedance variation graph. """
    fig = px.bar(title="Variação da Impedância (%)")
    fig.update_layout(
        template="plotly_white",
        yaxis_title="ΔZ (%)",
        xaxis_title="",
        paper_bgcolor="rgba(0,0,0,0)",
        plot_bgcolor=f"rgba({int(COLORS['background_card'][1:3], 16)},{int(COLORS['background_card'][3:5], 16)},{int(COLORS['background_card'][5:7], 16)},0.5)",
        font={"size": 10, "color": COLORS['text_light']},
        xaxis={"gridcolor": COLORS['border']},
        yaxis={"gridcolor": COLORS['border']},
        margin=dict(t=30, b=10, l=10, r=10),
        title_font_size=12,
        font_size=10
    )
    return fig

# --- Layout Definition Function ---
def create_short_circuit_layout():
    """Creates the layout component for the Short-Circuit section.

    Esta função cria o layout da seção de Curto-Circuito e inclui
    o painel de informações do transformador diretamente no layout, obtendo
    os dados do cache da aplicação.

    Returns:
        dash.html.Div: O layout completo da seção de Curto-Circuito
    """

    # transformer_data will be filled by callback, not here
    return dbc.Container([
        dbc.Row([
            dbc.Col(
                html.Div(
                    [
                        html.Div(id="transformer-info-short-circuit-page", className="mb-1"),
                        html.Div(html.Div(), id="transformer-info-short-circuit", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-losses", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-impulse", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-dieletric", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-applied", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-induced", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-temperature-rise", style={"display": "none"}),
                        html.Div(html.Div(), id="transformer-info-comprehensive", style={"display": "none"}),
                    ]
                )
            , width=12)
        ], className="mb-2"),
        dbc.Card([
            dbc.CardHeader([
                html.Span("Curto-Circuito", style={"fontWeight": "bold", "fontSize": "1.1rem", "color": COLORS["text_dark"]}),
                create_help_button("short-circuit-help"),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
            dbc.CardBody([
                create_labeled_input("Tempo de Duração (ms)", "short-circuit-duration", "Ex: 250", persistence=True, persistence_type="session"),
                create_labeled_input("Corrente de Curto (A)", "short-circuit-current", "Ex: 5000", persistence=True, persistence_type="session"),
                html.Div(id="short-circuit-corrente-info", style={"fontSize": "0.8rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
                html.Div(id="short-circuit-result", style={"fontWeight": "bold", "fontSize": "0.9rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
        ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "boxShadow": "0 1px 3px rgba(0,0,0,0.07)", "border": f"1px solid {COLORS['border']}"}),
        dbc.Card([
            dbc.CardHeader("Variação da Impedância (%)", style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "fontWeight": "bold", "fontSize": "1rem"}),
            dbc.CardBody([
                dcc.Graph(id="short-circuit-impedance-graph", figure=create_empty_sc_figure(), config={"displayModeBar": False}, style={"backgroundColor": COLORS["background_card_light"]}),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
        ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "boxShadow": "0 1px 3px rgba(0,0,0,0.07)", "border": f"1px solid {COLORS['border']}"}),
    ], fluid=True, style={"backgroundColor": COLORS["background_light"], "color": COLORS["text_dark"]})
