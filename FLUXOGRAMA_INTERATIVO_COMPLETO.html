<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fluxograma Interativo - Simulador de Transformadores</title>
    <script src="https://d3js.org/d3.v6.min.js"></script>
    <script src="https://unpkg.com/dagre@0.8.5/dist/dagre.min.js"></script>
    <script src="https://unpkg.com/dagre-d3@0.6.4/dist/dagre-d3.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            text-align: center;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn.active {
            background: linear-gradient(45deg, #00d2ff, #3a7bd5);
        }

        #flowchart {
            width: 100%;
            height: 800px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            overflow: auto;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node:hover {
            filter: brightness(1.2);
        }

        .node rect {
            stroke-width: 2;
            rx: 8;
            ry: 8;
        }

        .node.start rect {
            fill: #4CAF50;
            stroke: #2E7D32;
        }

        .node.process rect {
            fill: #2196F3;
            stroke: #1565C0;
        }

        .node.decision rect {
            fill: #FF9800;
            stroke: #E65100;
        }

        .node.calculation rect {
            fill: #9C27B0;
            stroke: #6A1B9A;
        }

        .node.output rect {
            fill: #F44336;
            stroke: #C62828;
        }

        .node.store rect {
            fill: #607D8B;
            stroke: #37474F;
        }

        .node text {
            fill: white;
            font-weight: bold;
            font-size: 12px;
        }

        .edge path {
            stroke: #fff;
            stroke-width: 2;
            fill: none;
        }

        .edge marker {
            fill: #fff;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 400px;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .legend {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .legend-color {
            width: 20px;
            height: 15px;
            margin-right: 10px;
            border-radius: 3px;
        }

        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            max-width: 300px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .variable-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .variable-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Fluxograma Interativo - Simulador de Transformadores</h1>

        <div class="controls">
            <button class="btn active" onclick="showFlow('main')">🏠 Fluxo Principal</button>
            <button class="btn" onclick="showFlow('initialization')">🚀 Inicialização</button>
            <button class="btn" onclick="showFlow('calculations')">🧮 Cálculos</button>
            <button class="btn" onclick="showFlow('data-flow')">📊 Fluxo de Dados</button>
            <button class="btn" onclick="showFlow('modules')">🧩 Módulos</button>
            <button class="btn" onclick="showFlow('validation')">✅ Validação</button>
            <button class="btn" onclick="showFlow('impulse_detailed')">⚡ Impulso Detalhado</button>
            <button class="btn" onclick="showFlow('losses_detailed')">🔋 Perdas Detalhado</button>
            <button class="btn" onclick="showFlow('error_handling')">🚨 Tratamento de Erros</button>
        </div>

        <svg id="flowchart"></svg>
    </div>

    <div class="legend">
        <h4>🎨 Legenda</h4>
        <div class="legend-item">
            <div class="legend-color" style="background: #4CAF50;"></div>
            <span>Início/Fim</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #2196F3;"></div>
            <span>Processo</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #FF9800;"></div>
            <span>Decisão</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #9C27B0;"></div>
            <span>Cálculo</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #F44336;"></div>
            <span>Saída/Resultado</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #607D8B;"></div>
            <span>Armazenamento</span>
        </div>
    </div>

    <div class="info-panel">
        <h4>📊 Informações do Nó</h4>
        <div id="node-info">Clique em um nó para ver detalhes</div>
        <div class="variable-list" id="variables-list"></div>

        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.3);">
            <h5>🔍 Buscar Variável</h5>
            <input type="text" id="search-variable" placeholder="Digite nome da variável..."
                   style="width: 100%; padding: 5px; border-radius: 3px; border: 1px solid #ccc; background: rgba(255,255,255,0.1); color: white;">
            <div id="search-results" style="margin-top: 10px; max-height: 150px; overflow-y: auto;"></div>
        </div>

        <div style="margin-top: 15px;">
            <button onclick="exportFlowchart()" style="width: 100%; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 5px;">
                📥 Exportar PNG
            </button>
            <button onclick="showFlowStats()" style="width: 100%; padding: 8px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 5px;">
                📊 Estatísticas
            </button>
            <button onclick="showHelp()" style="width: 100%; padding: 8px; background: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer;">
                ❓ Ajuda
            </button>
        </div>
    </div>

    <!-- Modal de Ajuda -->
    <div id="help-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 2000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #1e3c72; padding: 30px; border-radius: 15px; max-width: 600px; max-height: 80%; overflow-y: auto; color: white;">
            <h2>🔄 Guia do Fluxograma Interativo</h2>

            <h3>🎯 Como Usar:</h3>
            <ul>
                <li><strong>Navegação:</strong> Clique nos botões superiores para alternar entre fluxogramas</li>
                <li><strong>Zoom:</strong> Use a roda do mouse para zoom in/out</li>
                <li><strong>Pan:</strong> Arraste para mover o fluxograma</li>
                <li><strong>Detalhes:</strong> Clique em qualquer nó para ver informações detalhadas</li>
                <li><strong>Busca:</strong> Digite no campo de busca para encontrar variáveis específicas</li>
            </ul>

            <h3>⌨️ Atalhos do Teclado:</h3>
            <ul>
                <li><strong>Ctrl + S:</strong> Exportar fluxograma como PNG</li>
                <li><strong>Ctrl + F:</strong> Focar no campo de busca</li>
                <li><strong>Ctrl + I:</strong> Mostrar estatísticas do fluxograma</li>
            </ul>

            <h3>🎨 Cores dos Nós:</h3>
            <ul>
                <li><span style="color: #4CAF50;">🟢 Verde:</span> Início/Fim</li>
                <li><span style="color: #2196F3;">🔵 Azul:</span> Processo</li>
                <li><span style="color: #FF9800;">🟠 Laranja:</span> Decisão</li>
                <li><span style="color: #9C27B0;">🟣 Roxo:</span> Cálculo</li>
                <li><span style="color: #F44336;">🔴 Vermelho:</span> Saída/Resultado</li>
                <li><span style="color: #607D8B;">⚫ Cinza:</span> Armazenamento</li>
            </ul>

            <h3>📋 Fluxogramas Disponíveis:</h3>
            <ul>
                <li><strong>Fluxo Principal:</strong> Visão geral da aplicação</li>
                <li><strong>Inicialização:</strong> Processo de startup detalhado</li>
                <li><strong>Cálculos:</strong> Sistema de cálculos e fórmulas</li>
                <li><strong>Fluxo de Dados:</strong> Como os dados fluem pelo sistema</li>
                <li><strong>Módulos:</strong> Arquitetura modular</li>
                <li><strong>Validação:</strong> Sistema de validação de dados</li>
                <li><strong>Impulso Detalhado:</strong> Simulação de circuito de impulso</li>
                <li><strong>Perdas Detalhado:</strong> Cálculo de perdas do transformador</li>
                <li><strong>Tratamento de Erros:</strong> Sistema de tratamento de exceções</li>
            </ul>

            <button onclick="closeHelp()" style="margin-top: 20px; padding: 10px 20px; background: #F44336; color: white; border: none; border-radius: 5px; cursor: pointer;">
                ✖️ Fechar
            </button>
        </div>
    </div>

    <script>
        // Dados dos fluxogramas
        const flowcharts = {
            main: {
                title: "Fluxo Principal da Aplicação",
                nodes: [
                    {id: "start", label: "INÍCIO\napp.py", type: "start",
                     details: "Ponto de entrada da aplicação",
                     variables: ["config", "logging", "usage_limit", "app", "state_manager"]},
                    {id: "config", label: "Carregar\nconfig.py", type: "process",
                     details: "Carrega configurações da aplicação",
                     variables: ["LOG_DIR", "LOG_FILE", "APP_TITLE", "HOST", "PORT", "DEBUG_MODE"]},
                    {id: "logging", label: "Configurar\nLogging", type: "process",
                     details: "Configura sistema de logs",
                     variables: ["file_handler", "console_handler", "InputChangeFilter"]},
                    {id: "usage_check", label: "Verificar\nLimite de Uso", type: "decision",
                     details: "Verifica se o limite de uso foi atingido",
                     variables: ["uso_atual", "limite_atingido", "USAGE_LIMIT"]},
                    {id: "dash_init", label: "Inicializar\nDash App", type: "process",
                     details: "Cria instância do Dash com configurações",
                     variables: ["app", "server", "external_stylesheets", "meta_tags"]},
                    {id: "state_manager", label: "Inicializar\nStateManager", type: "process",
                     details: "Cria gerenciador de estado centralizado",
                     variables: ["state_manager", "mcp", "load_from_disk"]},
                    {id: "layout", label: "Criar Layout\nPrincipal", type: "process",
                     details: "Gera layout da interface",
                     variables: ["main_layout", "navbar", "sidebar", "content_area"]},
                    {id: "callbacks", label: "Registrar\nCallbacks", type: "process",
                     details: "Registra todos os callbacks da aplicação",
                     variables: ["decorated_modules", "explicit_registrations", "callback_map"]},
                    {id: "server_start", label: "Iniciar\nServidor", type: "output",
                     details: "Inicia servidor web",
                     variables: ["host", "port", "debug", "threaded"]}
                ],
                edges: [
                    {from: "start", to: "config"},
                    {from: "config", to: "logging"},
                    {from: "logging", to: "usage_check"},
                    {from: "usage_check", to: "dash_init"},
                    {from: "dash_init", to: "state_manager"},
                    {from: "state_manager", to: "layout"},
                    {from: "layout", to: "callbacks"},
                    {from: "callbacks", to: "server_start"}
                ]
            },

            calculations: {
                title: "Sistema de Cálculos e Fórmulas",
                nodes: [
                    {id: "input_data", label: "Dados de\nEntrada", type: "start",
                     details: "Dados inseridos pelo usuário",
                     variables: ["potencia_mva", "tensao_at", "tensao_bt", "frequencia", "impedancia"]},
                    {id: "validate_input", label: "Validar\nEntradas", type: "decision",
                     details: "Valida dados usando validators.py",
                     variables: ["is_valid_number", "is_positive_number", "is_required", "validate_dict_inputs"]},
                    {id: "losses_calc", label: "Cálculo de\nPerdas", type: "calculation",
                     details: "Calcula perdas em vazio e carga",
                     variables: [" calculate_no_losses", "calculate_load_losses", "perdas_vazio_kw", "perdas_carga_kw"]},
                    {id: "impulse_calc", label: "Cálculo de\nImpulso", type: "calculation",
                     details: "Simula circuito de impulso",
                     variables: ["simulate_hybrid_impulse", "rlc_solution", "k_factor_transform", "v_rlc", "alpha", "beta"]},
                    {id: "thermal_calc", label: "Cálculo\nTérmico", type: "calculation",
                     details: "Calcula elevação de temperatura",
                     variables: ["calculate_temperature_rise", "temp_amb", "winding_material", "res_cold", "res_hot"]},
                    {id: "dielectric_calc", label: "Análise\nDielétrica", type: "calculation",
                     details: "Calcula espaçamentos dielétricos",
                     variables: ["get_clearances", "fase_terra", "fase_fase", "outro_enrolamento", "NBR", "IEEE"]},
                    {id: "voltage_calc", label: "Cálculo de\nTensões", type: "calculation",
                     details: "Calcula tensões aplicada e induzida",
                     variables: ["calculate_applied_voltage", "calculate_induced_voltage", "Zc", "current", "power"]},
                    {id: "format_results", label: "Formatar\nResultados", type: "process",
                     details: "Formata resultados para exibição",
                     variables: ["format_number", "format_percentage", "format_voltage", "format_power"]},
                    {id: "display_results", label: "Exibir\nResultados", type: "output",
                     details: "Mostra resultados na interface",
                     variables: ["dbc.Card", "html.Div", "dash_table.DataTable"]}
                ],
                edges: [
                    {from: "input_data", to: "validate_input"},
                    {from: "validate_input", to: "losses_calc", label: "Válido"},
                    {from: "validate_input", to: "input_data", label: "Inválido"},
                    {from: "losses_calc", to: "impulse_calc"},
                    {from: "impulse_calc", to: "thermal_calc"},
                    {from: "thermal_calc", to: "dielectric_calc"},
                    {from: "dielectric_calc", to: "voltage_calc"},
                    {from: "voltage_calc", to: "format_results"},
                    {from: "format_results", to: "display_results"}
                ]
            },

            "data-flow": {
                title: "Fluxo de Dados e Estado",
                nodes: [
                    {id: "user_input", label: "Entrada do\nUsuário", type: "start",
                     details: "Usuário insere dados na interface",
                     variables: ["dcc.Input", "dcc.Dropdown", "dbc.Button", "n_clicks"]},
                    {id: "callback_trigger", label: "Callback\nTriggered", type: "process",
                     details: "Callback é acionado por mudança",
                     variables: ["@app.callback", "Input", "State", "Output", "callback_context"]},
                    {id: "get_state", label: "Obter Estado\nAtual", type: "process",
                     details: "Recupera dados do StateManager",
                     variables: ["state_manager.get", "state_manager.get_store", "transformer_data", "current_store_data"]},
                    {id: "process_data", label: "Processar\nDados", type: "process",
                     details: "Processa e valida dados",
                     variables: ["validate_dict_inputs", "convert_numpy_types", "clean_data"]},
                    {id: "perform_calc", label: "Executar\nCálculos", type: "calculation",
                     details: "Executa cálculos específicos do módulo",
                     variables: ["formulas.*", "math_functions", "numpy_arrays", "calculation_results"]},
                    {id: "update_mcp", label: "Atualizar\nMCP", type: "store",
                     details: "Salva dados no TransformerMCP",
                     variables: ["mcp.set_data", "auto_propagate", "_data", "_listeners"]},
                    {id: "propagate", label: "Propagar\nDados", type: "process",
                     details: "Propaga dados entre stores",
                     variables: ["ensure_mcp_data_propagation", "sync_isolation_values", "target_stores"]},
                    {id: "save_disk", label: "Salvar em\nDisco", type: "store",
                     details: "Persiste dados em arquivo JSON",
                     variables: ["save_mcp_to_disk", "mcp_state.json", "backup", "timestamp"]},
                    {id: "update_ui", label: "Atualizar\nInterface", type: "output",
                     details: "Atualiza componentes da UI",
                     variables: ["return_values", "dash.no_update", "component_updates"]}
                ],
                edges: [
                    {from: "user_input", to: "callback_trigger"},
                    {from: "callback_trigger", to: "get_state"},
                    {from: "get_state", to: "process_data"},
                    {from: "process_data", to: "perform_calc"},
                    {from: "perform_calc", to: "update_mcp"},
                    {from: "update_mcp", to: "propagate"},
                    {from: "propagate", to: "save_disk"},
                    {from: "save_disk", to: "update_ui"}
                ]
            },

            modules: {
                title: "Arquitetura de Módulos",
                nodes: [
                    {id: "transformer_inputs", label: "Dados do\nTransformador", type: "process",
                     details: "Módulo de entrada de dados básicos",
                     variables: ["potencia_mva", "frequencia", "tensao_at", "tensao_bt", "impedancia", "nbi_at", "sil_at"]},
                    {id: "losses", label: "Módulo de\nPerdas", type: "calculation",
                     details: "Cálculo de perdas em vazio e carga",
                     variables: ["perdas_vazio_kw", "perdas_carga_kw", "corrente_excitacao", "potencia_magnetica"]},
                    {id: "impulse", label: "Módulo de\nImpulso", type: "calculation",
                     details: "Simulação de circuito de impulso",
                     variables: ["v0_charge", "rf", "rt", "l_total", "c_gen", "c_load", "impulse_type"]},
                    {id: "dielectric", label: "Análise\nDielétrica", type: "calculation",
                     details: "Cálculo de espaçamentos dielétricos",
                     variables: ["um_kv", "bil_kvp", "sil_kvp", "fase_terra", "fase_fase", "clearances"]},
                    {id: "applied_voltage", label: "Tensão\nAplicada", type: "calculation",
                     details: "Cálculo de tensão aplicada",
                     variables: ["cap_at_pf", "cap_bt_pf", "cap_ter_pf", "Zc", "current", "power"]},
                    {id: "induced_voltage", label: "Tensão\nInduzida", type: "calculation",
                     details: "Cálculo de tensão induzida",
                     variables: ["inducao", "peso_nucleo", "perdas_vazio", "potencia_magnetica"]},
                    {id: "short_circuit", label: "Curto\nCircuito", type: "calculation",
                     details: "Análise de curto-circuito",
                     variables: ["impedancia_percent", "corrente_cc", "potencia_cc", "esforcos_mecanicos"]},
                    {id: "temperature", label: "Elevação de\nTemperatura", type: "calculation",
                     details: "Cálculo de elevação de temperatura",
                     variables: ["temp_amb", "winding_material", "res_cold", "res_hot", "delta_theta"]},
                    {id: "history", label: "Histórico", type: "store",
                     details: "Armazenamento de histórico",
                     variables: ["history.db", "session_data", "timestamps", "user_actions"]},
                    {id: "standards", label: "Normas\nTécnicas", type: "store",
                     details: "Consulta e gerenciamento de normas",
                     variables: ["standards.db", "NBR", "IEEE", "IEC", "ABNT"]}
                ],
                edges: [
                    {from: "transformer_inputs", to: "losses"},
                    {from: "transformer_inputs", to: "impulse"},
                    {from: "transformer_inputs", to: "dielectric"},
                    {from: "transformer_inputs", to: "applied_voltage"},
                    {from: "transformer_inputs", to: "induced_voltage"},
                    {from: "transformer_inputs", to: "short_circuit"},
                    {from: "transformer_inputs", to: "temperature"},
                    {from: "losses", to: "induced_voltage"},
                    {from: "dielectric", to: "history"},
                    {from: "impulse", to: "history"},
                    {from: "applied_voltage", to: "history"},
                    {from: "standards", to: "dielectric"}
                ]
            },

            validation: {
                title: "Sistema de Validação",
                nodes: [
                    {id: "input_received", label: "Entrada\nRecebida", type: "start",
                     details: "Dados recebidos do usuário",
                     variables: ["raw_input", "form_data", "user_values"]},
                    {id: "check_required", label: "Campos\nObrigatórios?", type: "decision",
                     details: "Verifica se campos obrigatórios estão preenchidos",
                     variables: ["is_required", "required_fields", "missing_fields"]},
                    {id: "check_numeric", label: "Valores\nNuméricos?", type: "decision",
                     details: "Valida se valores são numéricos",
                     variables: ["is_valid_number", "is_positive_number", "numeric_validation"]},
                    {id: "check_range", label: "Dentro do\nIntervalo?", type: "decision",
                     details: "Verifica se valores estão no intervalo válido",
                     variables: ["min_val", "max_val", "range_validation", "bounds_check"]},
                    {id: "check_allowed", label: "Valores\nPermitidos?", type: "decision",
                     details: "Verifica se valores estão na lista permitida",
                     variables: ["allowed_values", "dropdown_options", "enum_validation"]},
                    {id: "validation_error", label: "Erro de\nValidação", type: "output",
                     details: "Retorna mensagem de erro",
                     variables: ["error_message", "validation_errors", "user_feedback"]},
                    {id: "data_clean", label: "Dados\nLimpos", type: "process",
                     details: "Dados validados e limpos",
                     variables: ["clean_data", "validated_input", "processed_values"]},
                    {id: "proceed_calc", label: "Prosseguir\nCálculo", type: "output",
                     details: "Dados válidos, prosseguir com cálculos",
                     variables: ["valid_data", "calculation_ready", "proceed_flag"]}
                ],
                edges: [
                    {from: "input_received", to: "check_required"},
                    {from: "check_required", to: "validation_error", label: "Faltando"},
                    {from: "check_required", to: "check_numeric", label: "OK"},
                    {from: "check_numeric", to: "validation_error", label: "Inválido"},
                    {from: "check_numeric", to: "check_range", label: "OK"},
                    {from: "check_range", to: "validation_error", label: "Fora do intervalo"},
                    {from: "check_range", to: "check_allowed", label: "OK"},
                    {from: "check_allowed", to: "validation_error", label: "Não permitido"},
                    {from: "check_allowed", to: "data_clean", label: "OK"},
                    {from: "data_clean", to: "proceed_calc"}
                ]
            },

            initialization: {
                title: "Processo de Inicialização Detalhado",
                nodes: [
                    {id: "app_start", label: "app.py\nExecutado", type: "start",
                     details: "Início da execução",
                     variables: ["__name__", "__main__"]},
                    {id: "import_config", label: "Importar\nconfig.py", type: "process",
                     details: "Tenta importar configurações",
                     variables: ["config", "ConfigFallback"]},
                    {id: "config_success", label: "Config\nCarregado?", type: "decision",
                     details: "Verifica se config foi carregado com sucesso",
                     variables: ["ImportError", "fallback_config"]},
                    {id: "use_fallback", label: "Usar Config\nFallback", type: "process",
                     details: "Usa configuração de emergência",
                     variables: ["ConfigFallback", "LOG_DIR", "LOG_FILE", "APP_TITLE"]},
                    {id: "setup_logging", label: "Configurar\nSistema de Logs", type: "process",
                     details: "Configura handlers de log",
                     variables: ["file_handler", "console_handler", "root_logger", "InputChangeFilter"]},
                    {id: "usage_tracker", label: "Verificar\nContador de Uso", type: "process",
                     details: "Verifica e incrementa uso",
                     variables: ["UsageTracker", "db_path", "uso_atual", "limite_atingido"]},
                    {id: "dash_creation", label: "Criar Instância\nDash", type: "process",
                     details: "Cria aplicação Dash",
                     variables: ["dash.Dash", "external_stylesheets", "suppress_callback_exceptions"]},
                    {id: "mcp_init", label: "Inicializar\nTransformerMCP", type: "process",
                     details: "Cria sistema de persistência",
                     variables: ["TransformerMCP", "STORE_IDS", "_data", "_listeners"]},
                    {id: "load_disk", label: "Carregar Dados\ndo Disco", type: "process",
                     details: "Carrega estado salvo",
                     variables: ["mcp_data.json", "stores", "timestamp", "version"]},
                    {id: "create_layout", label: "Gerar Layout\nCompleto", type: "process",
                     details: "Cria interface completa",
                     variables: ["create_main_layout", "navbar", "sidebar", "global_stores"]},
                    {id: "register_callbacks", label: "Registrar Todos\nos Callbacks", type: "process",
                     details: "Registra callbacks decorados e explícitos",
                     variables: ["decorated_modules", "explicit_registrations", "app.callback_map"]},
                    {id: "start_server", label: "Iniciar Servidor\nWeb", type: "output",
                     details: "Inicia servidor Flask/Dash",
                     variables: ["app.run", "host", "port", "debug", "use_reloader"]}
                ],
                edges: [
                    {from: "app_start", to: "import_config"},
                    {from: "import_config", to: "config_success"},
                    {from: "config_success", to: "use_fallback", label: "Erro"},
                    {from: "config_success", to: "setup_logging", label: "Sucesso"},
                    {from: "use_fallback", to: "setup_logging"},
                    {from: "setup_logging", to: "usage_tracker"},
                    {from: "usage_tracker", to: "dash_creation"},
                    {from: "dash_creation", to: "mcp_init"},
                    {from: "mcp_init", to: "load_disk"},
                    {from: "load_disk", to: "create_layout"},
                    {from: "create_layout", to: "register_callbacks"},
                    {from: "register_callbacks", to: "start_server"}
                ]
            }
        };

        let currentFlow = 'main';
        let svg, g, tooltip;

        function initializeFlowchart() {
            try {
                console.log('Inicializando fluxograma...');

                svg = d3.select("#flowchart");
                if (svg.empty()) {
                    console.error('Elemento SVG não encontrado');
                    return;
                }

                g = svg.append("g");

                // Criar tooltip
                tooltip = d3.select("body").append("div")
                    .attr("class", "tooltip")
                    .style("opacity", 0)
                    .style("position", "absolute")
                    .style("pointer-events", "none");

                // Adicionar zoom
                const zoom = d3.zoom()
                    .scaleExtent([0.1, 3])
                    .on("zoom", (event) => {
                        g.attr("transform", event.transform);
                    });

                svg.call(zoom);

                // Adicionar indicador de carregamento
                g.append("text")
                    .attr("x", 400)
                    .attr("y", 300)
                    .attr("text-anchor", "middle")
                    .style("fill", "white")
                    .style("font-size", "18px")
                    .text("Carregando fluxograma...");

                // Aguardar um pouco para garantir que as bibliotecas carregaram
                setTimeout(() => {
                    showFlow('main');
                }, 500);

                console.log('Fluxograma inicializado com sucesso');

            } catch (error) {
                console.error('Erro ao inicializar fluxograma:', error);

                // Fallback: mostrar mensagem de erro
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: white;">
                        <h3>⚠️ Erro ao carregar fluxograma</h3>
                        <p>Verifique sua conexão com a internet e recarregue a página.</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            🔄 Recarregar
                        </button>
                    </div>
                `;
                document.getElementById('flowchart').parentNode.appendChild(errorDiv);
            }
        }

        // Adicionar mais fluxogramas específicos
        flowcharts.impulse_detailed = {
            title: "Fluxo Detalhado - Módulo de Impulso",
            nodes: [
                {id: "impulse_start", label: "Entrada\nImpulso", type: "start",
                 details: "Dados de entrada para simulação de impulso",
                 variables: ["v0_charge", "rf", "rt", "l_total", "c_gen", "c_load", "impulse_type", "gap_distance_cm"]},
                {id: "validate_impulse", label: "Validar\nParâmetros", type: "decision",
                 details: "Valida parâmetros de entrada do impulso",
                 variables: ["voltage_range", "resistance_range", "inductance_range", "capacitance_range"]},
                {id: "calc_ceq", label: "Calcular\nC_eq", type: "calculation",
                 details: "c_eq = (c_gen * c_load) / (c_gen + c_load)",
                 variables: ["c_eq", "c_gen", "c_load", "parallel_capacitance"]},
                {id: "rlc_solution", label: "Solução\nRLC", type: "calculation",
                 details: "Resolve circuito RLC no domínio do tempo",
                 variables: ["v_rlc", "r_rlc", "l_total", "c_eq", "time_array", "differential_equation"]},
                {id: "k_factor", label: "Transformação\nK-Factor", type: "calculation",
                 details: "Aplica transformação K-factor para correção",
                 variables: ["k_factor", "v_test", "v_base", "overshoot", "alpha_fit", "beta_fit"]},
                {id: "double_exp", label: "Dupla\nExponencial", type: "calculation",
                 details: "Ajuste com função dupla exponencial",
                 variables: ["alpha", "beta", "A", "B", "t1", "t2", "exponential_fit"]},
                {id: "chop_analysis", label: "Análise de\nCorte", type: "decision",
                 details: "Verifica se é impulso cortado",
                 variables: ["impulse_type", "gap_distance", "chop_time", "breakdown_voltage"]},
                {id: "calc_chop", label: "Calcular\nTempo de Corte", type: "calculation",
                 details: "Calcula tempo de colapso do gap",
                 variables: ["chop_time_sec", "gap_distance_cm", "breakdown_field", "collapse_time"]},
                {id: "final_waveform", label: "Forma de Onda\nFinal", type: "output",
                 details: "Gera forma de onda final do impulso",
                 variables: ["v_final", "i_load", "time_to_peak", "peak_value", "tail_time"]}
            ],
            edges: [
                {from: "impulse_start", to: "validate_impulse"},
                {from: "validate_impulse", to: "calc_ceq", label: "Válido"},
                {from: "validate_impulse", to: "impulse_start", label: "Inválido"},
                {from: "calc_ceq", to: "rlc_solution"},
                {from: "rlc_solution", to: "k_factor"},
                {from: "k_factor", to: "double_exp"},
                {from: "double_exp", to: "chop_analysis"},
                {from: "chop_analysis", to: "calc_chop", label: "Cortado"},
                {from: "chop_analysis", to: "final_waveform", label: "Normal"},
                {from: "calc_chop", to: "final_waveform"}
            ]
        };

        flowcharts.losses_detailed = {
            title: "Fluxo Detalhado - Módulo de Perdas",
            nodes: [
                {id: "losses_start", label: "Entrada\nPerdas", type: "start",
                 details: "Dados de entrada para cálculo de perdas",
                 variables: ["tensao_bt", "inducao", "peso_nucleo", "corrente", "impedancia_percent", "temperatura"]},
                {id: "empty_losses", label: "Perdas em\nVazio", type: "calculation",
                 details: "Calcula perdas no núcleo em vazio",
                 variables: ["fator_perdas", "perdas_vazio_kw", "corrente_excitacao", "potencia_magnetica"]},
                {id: "load_losses", label: "Perdas em\nCarga", type: "calculation",
                 details: "Calcula perdas nos enrolamentos",
                 variables: ["perdas_carga_kw", "tensao_cc", "potencia_teste", "fator_correcao_temp"]},
                {id: "temp_correction", label: "Correção de\nTemperatura", type: "calculation",
                 details: "Aplica correção de temperatura nas perdas",
                 variables: ["temp_teste", "temp_referencia", "fator_correcao", "material_condutor"]},
                {id: "efficiency_calc", label: "Cálculo de\nRendimento", type: "calculation",
                 details: "Calcula rendimento do transformador",
                 variables: ["rendimento", "perdas_totais", "potencia_nominal", "fator_carga"]},
                {id: "losses_results", label: "Resultados\nPerdas", type: "output",
                 details: "Apresenta resultados finais",
                 variables: ["perdas_vazio_final", "perdas_carga_final", "rendimento_final", "classe_rendimento"]}
            ],
            edges: [
                {from: "losses_start", to: "empty_losses"},
                {from: "empty_losses", to: "load_losses"},
                {from: "load_losses", to: "temp_correction"},
                {from: "temp_correction", to: "efficiency_calc"},
                {from: "efficiency_calc", to: "losses_results"}
            ]
        };

        flowcharts.error_handling = {
            title: "Sistema de Tratamento de Erros",
            nodes: [
                {id: "operation_start", label: "Operação\nIniciada", type: "start",
                 details: "Início de qualquer operação no sistema",
                 variables: ["operation_type", "input_data", "context"]},
                {id: "try_block", label: "Bloco\nTry", type: "process",
                 details: "Execução protegida da operação",
                 variables: ["try_block", "protected_operation", "execution_context"]},
                {id: "operation_success", label: "Operação\nSucesso?", type: "decision",
                 details: "Verifica se operação foi bem-sucedida",
                 variables: ["success_flag", "result_data", "operation_status"]},
                {id: "log_success", label: "Log de\nSucesso", type: "process",
                 details: "Registra operação bem-sucedida",
                 variables: ["log.info", "success_message", "operation_details"]},
                {id: "catch_exception", label: "Capturar\nExceção", type: "process",
                 details: "Captura e processa exceções",
                 variables: ["Exception", "error_type", "error_message", "stack_trace"]},
                {id: "log_error", label: "Log de\nErro", type: "process",
                 details: "Registra erro no sistema de logs",
                 variables: ["log.error", "error_details", "exc_info", "timestamp"]},
                {id: "user_feedback", label: "Feedback\nUsuário", type: "output",
                 details: "Mostra mensagem de erro para o usuário",
                 variables: ["error_alert", "user_message", "recovery_options"]},
                {id: "return_result", label: "Retornar\nResultado", type: "output",
                 details: "Retorna resultado da operação",
                 variables: ["return_value", "dash.no_update", "success_data"]}
            ],
            edges: [
                {from: "operation_start", to: "try_block"},
                {from: "try_block", to: "operation_success"},
                {from: "operation_success", to: "log_success", label: "Sucesso"},
                {from: "operation_success", to: "catch_exception", label: "Erro"},
                {from: "log_success", to: "return_result"},
                {from: "catch_exception", to: "log_error"},
                {from: "log_error", to: "user_feedback"},
                {from: "user_feedback", to: "return_result"}
            ]
        };

        function showFlow(flowType) {
            // Atualizar botões
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            currentFlow = flowType;
            if (flowcharts[flowType]) {
                renderFlowchart(flowcharts[flowType]);
            } else {
                console.error(`Fluxograma '${flowType}' não encontrado`);
            }
        }

        function renderFlowchart(flowData) {
            try {
                // Limpar SVG
                g.selectAll("*").remove();

                // Verificar se dagre-d3 está disponível
                if (typeof dagreD3 === 'undefined') {
                    console.error('dagre-d3 não carregado');
                    renderSimpleFlowchart(flowData);
                    return;
                }

                // Criar grafo dirigido
                const graph = new dagreD3.graphlib.Graph()
                    .setGraph({
                        rankdir: 'TB',
                        nodesep: 50,
                        ranksep: 50,
                        marginx: 20,
                        marginy: 20
                    })
                    .setDefaultEdgeLabel(() => ({}));

                // Adicionar nós
                flowData.nodes.forEach(node => {
                    graph.setNode(node.id, {
                        label: node.label,
                        class: node.type,
                        width: 120,
                        height: 60,
                        rx: 8,
                        ry: 8
                    });
                });

                // Adicionar arestas
                flowData.edges.forEach(edge => {
                    graph.setEdge(edge.from, edge.to, {
                        label: edge.label || "",
                        curve: d3.curveBasis
                    });
                });

                // Layout
                dagreD3.layout(graph);

                // Renderizar
                const render = new dagreD3.render();
                render(g, graph);

                // Aplicar estilos personalizados
                g.selectAll(".node rect")
                    .style("stroke-width", "2px")
                    .style("rx", "8px")
                    .style("ry", "8px");

                g.selectAll(".node text")
                    .style("fill", "white")
                    .style("font-weight", "bold")
                    .style("font-size", "12px");

                g.selectAll(".edgePath path")
                    .style("stroke", "#fff")
                    .style("stroke-width", "2px")
                    .style("fill", "none");

                // Adicionar interatividade
                g.selectAll(".node")
                    .style("cursor", "pointer")
                    .on("click", function(event, d) {
                        const nodeData = flowData.nodes.find(n => n.id === d);
                        if (nodeData) showNodeInfo(nodeData);
                    })
                    .on("mouseover", function(event, d) {
                        const nodeData = flowData.nodes.find(n => n.id === d);
                        if (nodeData) showTooltip(event, nodeData);
                        d3.select(this).style("filter", "brightness(1.2)");
                    })
                    .on("mouseout", function() {
                        hideTooltip();
                        d3.select(this).style("filter", "none");
                    });

                // Centralizar
                setTimeout(() => {
                    try {
                        const svgRect = svg.node().getBoundingClientRect();
                        const graphBounds = g.node().getBBox();
                        const centerX = Math.max(0, (svgRect.width - graphBounds.width) / 2);
                        const centerY = Math.max(0, (svgRect.height - graphBounds.height) / 2);

                        svg.call(d3.zoom().transform, d3.zoomIdentity.translate(centerX, centerY));
                    } catch (e) {
                        console.warn('Erro ao centralizar:', e);
                    }
                }, 100);

            } catch (error) {
                console.error('Erro ao renderizar fluxograma:', error);
                renderSimpleFlowchart(flowData);
            }
        }

        // Função de fallback para renderização simples
        function renderSimpleFlowchart(flowData) {
            g.selectAll("*").remove();

            const nodeWidth = 120;
            const nodeHeight = 60;
            const spacing = 150;

            // Renderizar nós simples
            flowData.nodes.forEach((node, index) => {
                const x = (index % 3) * spacing + 100;
                const y = Math.floor(index / 3) * spacing + 100;

                const nodeGroup = g.append("g")
                    .attr("class", "node")
                    .attr("transform", `translate(${x}, ${y})`)
                    .style("cursor", "pointer");

                // Retângulo do nó
                nodeGroup.append("rect")
                    .attr("width", nodeWidth)
                    .attr("height", nodeHeight)
                    .attr("rx", 8)
                    .attr("ry", 8)
                    .style("fill", getNodeColor(node.type))
                    .style("stroke", "#fff")
                    .style("stroke-width", "2px");

                // Texto do nó
                nodeGroup.append("text")
                    .attr("x", nodeWidth / 2)
                    .attr("y", nodeHeight / 2)
                    .attr("text-anchor", "middle")
                    .attr("dominant-baseline", "middle")
                    .style("fill", "white")
                    .style("font-weight", "bold")
                    .style("font-size", "12px")
                    .text(node.label.replace('\n', ' '));

                // Adicionar interatividade
                nodeGroup
                    .on("click", () => showNodeInfo(node))
                    .on("mouseover", (event) => {
                        showTooltip(event, node);
                        nodeGroup.style("filter", "brightness(1.2)");
                    })
                    .on("mouseout", () => {
                        hideTooltip();
                        nodeGroup.style("filter", "none");
                    });
            });
        }

        function getNodeColor(type) {
            const colors = {
                start: "#4CAF50",
                process: "#2196F3",
                decision: "#FF9800",
                calculation: "#9C27B0",
                output: "#F44336",
                store: "#607D8B"
            };
            return colors[type] || "#607D8B";
        }

        function showTooltip(event, nodeData) {
            tooltip.transition()
                .duration(200)
                .style("opacity", .9);

            tooltip.html(`
                <strong>${nodeData.label.replace('\n', ' ')}</strong><br/>
                <em>Tipo:</em> ${nodeData.type}<br/>
                <em>Detalhes:</em> ${nodeData.details}
            `)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 28) + "px");
        }

        function hideTooltip() {
            tooltip.transition()
                .duration(500)
                .style("opacity", 0);
        }

        function showNodeInfo(nodeData) {
            const infoPanel = document.getElementById('node-info');
            const variablesList = document.getElementById('variables-list');

            infoPanel.innerHTML = `
                <strong>${nodeData.label.replace('\n', ' ')}</strong><br/>
                <em>Tipo:</em> ${nodeData.type}<br/>
                <em>Detalhes:</em> ${nodeData.details}<br/>
                <button id="explain-step-btn" style="margin-top:8px; padding:6px 12px; background:#FF9800; color:white; border:none; border-radius:5px; cursor:pointer; font-size:12px;">
                    💡 Explicar este passo
                </button>
                <div id="step-explanation" style="margin-top:10px; color:#ffe082;"></div>
            `;

            if (nodeData.variables && nodeData.variables.length > 0) {
                variablesList.innerHTML = '<h5>📋 Variáveis:</h5>' +
                    nodeData.variables.map(v => `<div class="variable-item">${v}</div>`).join('');
            } else {
                variablesList.innerHTML = '';
            }

            // Explicações detalhadas por id de nó (exemplo inicial, pode ser expandido)
            const stepExplanations = {
                'start': 'Este é o ponto de entrada do sistema. Aqui o aplicativo é iniciado, carregando configurações e preparando o ambiente para o usuário.',
                'config': 'Carrega as configurações principais do sistema, como diretórios, arquivos de log e parâmetros de inicialização.',
                'logging': 'Configura o sistema de logs, permitindo registrar eventos importantes para auditoria e depuração.',
                'usage_check': 'Verifica se o limite de uso do simulador foi atingido, protegendo contra uso excessivo.',
                'dash_init': 'Inicializa o aplicativo Dash, que é responsável pela interface web interativa.',
                'state_manager': 'Cria o gerenciador de estado centralizado, responsável por armazenar e propagar dados entre os módulos.',
                'layout': 'Gera o layout principal da interface, organizando menus, painéis e áreas de conteúdo.',
                'callbacks': 'Registra todos os callbacks (funções reativas) que respondem a interações do usuário.',
                'server_start': 'Inicia o servidor web, tornando o simulador acessível pelo navegador.',
                'input_data': 'Aqui o usuário insere os dados básicos do transformador, como potência, tensões e impedância.',
                'validate_input': 'Valida os dados inseridos, garantindo que estejam corretos e completos antes de prosseguir.',
                'losses_calc': 'Calcula as perdas elétricas do transformador, tanto em vazio quanto em carga.',
                'impulse_calc': 'Simula o ensaio de impulso, importante para verificar a resistência do transformador a surtos elétricos.',
                'thermal_calc': 'Calcula a elevação de temperatura dos enrolamentos, fundamental para a segurança e vida útil do equipamento.',
                'dielectric_calc': 'Analisa os espaçamentos dielétricos, verificando se atendem às normas de isolamento.',
                'voltage_calc': 'Calcula as tensões aplicadas e induzidas, essenciais para testes de conformidade.',
                'format_results': 'Formata os resultados dos cálculos para exibição clara ao usuário.',
                'display_results': 'Exibe os resultados finais na interface, permitindo análise e exportação.',
                // ...adicionar mais explicações conforme necessário...
            };

            document.getElementById('explain-step-btn').onclick = function() {
                const explanation = stepExplanations[nodeData.id] || 'Explicação detalhada não disponível para este passo. Consulte a documentação para mais informações.';
                document.getElementById('step-explanation').innerHTML = explanation;
            };
        }

        // Função de busca de variáveis
        function searchVariables() {
            const searchTerm = document.getElementById('search-variable').value.toLowerCase();
            const resultsDiv = document.getElementById('search-results');

            if (searchTerm.length < 2) {
                resultsDiv.innerHTML = '';
                return;
            }

            const results = [];
            const currentFlowData = flowcharts[currentFlow];

            if (currentFlowData && currentFlowData.nodes) {
                currentFlowData.nodes.forEach(node => {
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.toLowerCase().includes(searchTerm)) {
                                results.push({
                                    variable: variable,
                                    node: node.label.replace('\n', ' '),
                                    nodeId: node.id,
                                    type: node.type
                                });
                            }
                        });
                    }
                });
            }

            if (results.length > 0) {
                resultsDiv.innerHTML = '<h6>🎯 Resultados:</h6>' +
                    results.map(r => `
                        <div class="variable-item" onclick="highlightNode('${r.nodeId}')" style="cursor: pointer; border-left: 3px solid #4CAF50;">
                            <strong>${r.variable}</strong><br/>
                            <small>em: ${r.node} (${r.type})</small>
                        </div>
                    `).join('');
            } else {
                resultsDiv.innerHTML = '<div style="color: #ff6b6b; font-size: 12px;">Nenhuma variável encontrada</div>';
            }
        }

        // Função para destacar nó
        function highlightNode(nodeId) {
            g.selectAll('.node').style('opacity', 0.3);
            g.select(`#${nodeId}`).style('opacity', 1).style('filter', 'drop-shadow(0 0 10px #4CAF50)');

            setTimeout(() => {
                g.selectAll('.node').style('opacity', 1).style('filter', 'none');
            }, 3000);
        }

        // Função para exportar fluxograma
        function exportFlowchart() {
            const svgElement = document.getElementById('flowchart');
            const svgData = new XMLSerializer().serializeToString(svgElement);

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            canvas.width = svgElement.clientWidth;
            canvas.height = svgElement.clientHeight;

            img.onload = function() {
                ctx.fillStyle = '#1e3c72';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0);

                const link = document.createElement('a');
                link.download = `fluxograma_${currentFlow}_${new Date().getTime()}.png`;
                link.href = canvas.toDataURL();
                link.click();
            };

            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }

        // Adicionar event listener para busca
        document.addEventListener('DOMContentLoaded', function() {
            initializeFlowchart();

            const searchInput = document.getElementById('search-variable');
            if (searchInput) {
                searchInput.addEventListener('input', searchVariables);
            }
        });

        // Função para mostrar estatísticas do fluxograma atual
        function showFlowStats() {
            const flowData = flowcharts[currentFlow];
            if (!flowData) return;

            const stats = {
                nodes: flowData.nodes.length,
                edges: flowData.edges.length,
                variables: flowData.nodes.reduce((total, node) => total + (node.variables ? node.variables.length : 0), 0),
                types: [...new Set(flowData.nodes.map(n => n.type))].length
            };

            alert(`📊 Estatísticas do Fluxograma "${flowData.title}":

🔹 Nós: ${stats.nodes}
🔹 Conexões: ${stats.edges}
🔹 Variáveis: ${stats.variables}
🔹 Tipos diferentes: ${stats.types}`);
        }

        // Funções do modal de ajuda
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function closeHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // Fechar modal clicando fora
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('help-modal');
            if (event.target === modal) {
                closeHelp();
            }
        });

        // Adicionar teclas de atalho
        document.addEventListener('keydown', function(event) {
            // Fechar modal com ESC
            if (event.key === 'Escape') {
                closeHelp();
            }

            if (event.ctrlKey) {
                switch(event.key) {
                    case 's':
                        event.preventDefault();
                        exportFlowchart();
                        break;
                    case 'f':
                        event.preventDefault();
                        document.getElementById('search-variable').focus();
                        break;
                    case 'i':
                        event.preventDefault();
                        showFlowStats();
                        break;
                    case 'h':
                        event.preventDefault();
                        showHelp();
                        break;
                }
            }
        });

        // Função para mostrar informações sobre o código
        function showCodeInfo() {
            const info = `
🔄 FLUXOGRAMA INTERATIVO - SIMULADOR DE TRANSFORMADORES

📊 RESUMO TÉCNICO:
• Linguagem: HTML5 + JavaScript + D3.js + Dagre-D3
• Arquitetura: MVC com StateManager centralizado
• Persistência: JSON em disco + Session Storage
• Framework: Dash (Python) + Flask
• Validação: Sistema robusto com validators.py
• Cálculos: Módulos matemáticos especializados
• Interface: Bootstrap + CSS customizado

🧮 MÓDULOS DE CÁLCULO:
• Perdas:  calculate_no_losses, calculate_load_losses
• Impulso: simulate_hybrid_impulse, rlc_solution, k_factor_transform
• Térmico: calculate_temperature_rise
• Dielétrico: get_clearances (NBR/IEEE)
• Tensões: calculate_applied_voltage, calculate_induced_voltage

📁 ESTRUTURA DE DADOS:
• TransformerMCP: Gerenciamento centralizado de estado
• Global Stores: transformer-inputs-store, losses-store, impulse-store, etc.
• Persistência: mcp_state.json, history.db, standards.db

🔧 FUNCIONALIDADES:
• Validação robusta de entradas
• Cálculos matemáticos complexos
• Geração de relatórios PDF
• Sistema de histórico
• Consulta a normas técnicas
• Interface responsiva e interativa

⚡ PERFORMANCE:
• Callbacks otimizados com prevent_initial_call
• Conversão automática de tipos numpy
• Sistema de backup automático
• Logs estruturados para debugging
            `;

            alert(info);
        }
    </script>
</body>
</html>
