"""
Callbacks para controle de logging.
Este módulo fornece callbacks para controlar o nível de logging da aplicação.
"""
import logging
from dash import callback, Input, Output, State, html
import dash_bootstrap_components as dbc

# Obter logger
log = logging.getLogger(__name__)

# Registrar callbacks usando decoradores
@callback(
    Output("logging-status-container", "children"),
    Input("toggle-logging-button", "n_clicks"),
    State("logging-status-container", "children"),
    prevent_initial_call=True
)
def toggle_logging_mode(n_clicks, current_status):
    """
    Alterna o modo de logging detalhado quando o botão é clicado.

    Args:
        n_clicks: Número de cliques no botão
        current_status: Status atual do logging

    Returns:
        Novo status do logging
    """
    if not n_clicks:
        return current_status

    # Obter a instância do app
    from app import app

    try:
        # Alternar o modo de logging
        new_state = app.toggle_detailed_logging()
    except Exception as e:
        # Em caso de erro, usar a função diretamente
        from utils.input_change_logger import enable_detailed_logging

        # Determinar o estado atual
        current_state = "ATIVADO" in str(current_status)
        # Inverter o estado
        new_state = not current_state
        # Aplicar o novo estado
        enable_detailed_logging(new_state)

    # Criar mensagem de status
    if new_state:
        return [
            html.Span("Logging: ", className="fw-bold"),
            html.Span("DETALHADO", className="text-success fw-bold"),
            html.Small(" (todos os logs)", className="text-muted ms-2")
        ]
    else:
        return [
            html.Span("Logging: ", className="fw-bold"),
            html.Span("SIMPLIFICADO", className="text-danger fw-bold"),
            html.Small(" (apenas alterações)", className="text-muted ms-2")
        ]

# Não é necessária uma função de registro explícito, pois os callbacks já são registrados usando decoradores

# Função para criar o componente de controle de logging
def create_logging_control():
    """
    Cria o componente de controle de logging.

    Returns:
        Componente Dash para controle de logging
    """
    return dbc.Card(
        dbc.CardBody([
            html.Div([
                html.H6("Controle de Logging", className="card-title mb-2"),
                html.Hr(className="my-1"),
                html.Div(
                    [
                        html.Span("Logging: ", className="fw-bold"),
                        html.Span("SIMPLIFICADO", className="text-danger fw-bold"),
                        html.Small(" (apenas alterações)", className="text-muted ms-2")
                    ],
                    id="logging-status-container",
                    className="my-2"
                ),
                dbc.Button(
                    [
                        html.I(className="fas fa-toggle-on me-1"),
                        "Alternar Modo de Log"
                    ],
                    id="toggle-logging-button",
                    color="secondary",
                    size="sm",
                    className="mt-2 w-100",
                    title="Clique para alternar entre o modo simplificado e o modo detalhado"
                ),
                html.Small(
                    [
                        "Use este controle para alternar entre o modo simplificado (apenas alterações importantes) e o modo detalhado (todos os logs).",
                        html.Br(),
                        html.Strong("Atenção: ", className="text-warning"),
                        "O modo detalhado pode gerar muitos logs e afetar o desempenho."
                    ],
                    className="text-muted mt-2 d-block"
                )
            ])
        ]),
        className="mb-3 shadow-sm",
        style={"fontSize": "0.8rem"}
    )
