2025-05-24 12:38:18 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 12:38:18 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 12:38:18 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 12:38:18 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 12:38:18 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 12:38:18 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 12:38:18 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 12:38:18 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 12:38:18 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 12:38:18 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 12:38:18 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 12:40:36 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 12:40:36 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 12:40:36 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 12:40:36 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 12:40:36 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 12:40:36 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 12:40:36 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 12:40:36 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 12:40:36 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 12:40:36 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 12:40:36 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 13:28:10 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:28:10 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 13:28:10 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 13:28:10 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 13:28:10 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 13:28:10 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:28:10 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:28:10 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 13:28:10 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 13:28:10 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 13:28:10 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 13:28:13 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 13:28:13 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:28:13 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 13:28:13 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: 1
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 1
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/
2025-05-24 13:28:13 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: teste_tensao_aplicada_at
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:28:13 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:28:13 - utils.elec - ERROR - [elec] Erro inesperado no cálculo: float division by zero
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\elec.py", line 112, in calculate_nominal_currents
    (potencia * 1000) / (sqrt3 * tensao_terciario), 2
    ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~
ZeroDivisionError: float division by zero
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] NÃO sobrescrevendo nbi_at: valor salvo no MCP mantido (1800.0)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] NÃO sobrescrevendo nbi_neutro_at: valor salvo no MCP mantido (20.0)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] NÃO sobrescrevendo teste_tensao_aplicada_at: valor salvo no MCP mantido (10.0)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] NÃO sobrescrevendo teste_tensao_induzida_at: valor salvo no MCP mantido (10.0)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] NÃO sobrescrevendo teste_tensao_aplicada_bt: valor salvo no MCP mantido (10.0)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:28:13 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Mantendo compatibilidade: teste_tensao_induzida=10.0
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 13:28:13 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-24 13:28:13 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-24 13:28:13 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] StateManager atualizado e salvo no disco.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Store de origem transformer-inputs-store É a fonte-de-verdade para campos básicos
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para losses-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'impulse-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para impulse-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'dieletric-analysis-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para dieletric-analysis-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'applied-voltage-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para applied-voltage-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'induced-voltage-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para induced-voltage-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'short-circuit-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para short-circuit-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'temperature-rise-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para temperature-rise-store
2025-05-24 13:28:13 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:28:13 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'comprehensive-analysis-store'.
2025-05-24 13:28:13 - utils.mcp_persistence - INFO - [ensure_mcp_data_propagation] Dados básicos propagados para comprehensive-analysis-store
2025-05-24 13:28:13 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Dados propagados para todos os stores alvo via StateManager.
2025-05-24 13:43:29 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:43:29 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 13:43:29 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 13:43:29 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 13:43:29 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 13:43:29 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:43:29 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 13:43:29 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 13:43:29 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 13:43:29 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 13:43:29 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 13:43:33 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:43:33 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 13:43:33 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 13:43:33 - callbacks.global_updates - DEBUG - Painel atualizado em 1.5ms
2025-05-24 13:43:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 13:43:33 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: 1
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 1
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:43:33 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:43:36 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 13:43:36 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-24 13:43:36 - layouts.losses - INFO - Creating Losses layout...
2025-05-24 13:43:36 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: 1, Path: /perdas
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: 1, Path: /perdas
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: 1
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 1
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/perdas
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:43:37 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:43:37 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:43:37 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-24 13:43:38 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:43:40 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:43:41 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-24 13:43:41 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-24 13:43:41 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-24 13:43:41 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:43:42 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 13:43:42 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-24 13:43:42 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-24 13:43:42 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-24 13:43:42 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-24 13:43:42 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:42 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 13:43:42 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-24 13:43:48 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:43:48 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'dieletric-analysis-store'.
2025-05-24 13:43:48 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:48 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:43:48 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:48 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:48 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 13:43:48 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 13:43:48 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-24 13:43:53 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 13:43:53 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 13:43:53 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:43:53 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 13:43:53 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /dados
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: 1
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /dados
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 1
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/dados
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:43:53 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:43:55 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 13:43:55 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/tensao-aplicada, clean_path=tensao-aplicada), prevenindo atualização
2025-05-24 13:43:55 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: 1, Path: /tensao-aplicada
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: 1, Path: /tensao-aplicada
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /tensao-aplicada
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=1800.0, SIL=None, TA=10.0, TI=10.0
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=10.0, TI=/tensao-aplicada
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: 1
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 1
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/tensao-aplicada
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=20.0, SIL=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Verificando se valor salvo de NBI (1800.0) já está nas opções: True
2025-05-24 13:43:55 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Opções disponíveis para NBI: ['20.0', '40.0', '60.0', '75.0', '95.0', '110.0', '125.0', '145.0', '170.0', '200.0', '250.0', '325.0', '350.0', '450.0', '550.0', '650.0', '750.0', '850.0', '950.0', '1050.0', '1175.0', '1300.0', '1425.0', '1550.0', '1800.0', '1950.0', '2100.0']
2025-05-24 13:43:55 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 13:43:55 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'losses-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'impulse-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'front-resistor-data'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'tail-resistor-data'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'calculated-inductance'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'simulation-status'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'dieletric-analysis-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'applied-voltage-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'induced-voltage-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'short-circuit-store'.
2025-05-24 13:44:04 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:04 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'temperature-rise-store'.
2025-05-24 13:44:04 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:04 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:04 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:44:07 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 13:44:07 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 13:44:07 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 13:44:07 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:07 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:07 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:44:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 13:44:07 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /dados
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: 1
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: 1, Path: /dados
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: 1
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:07 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:14 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:14 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 13:44:14 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: transformer-inputs-store
2025-05-24 13:44:14 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:14 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Usando dados frescos do state_manager
2025-05-24 13:44:14 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:44:14 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Usando dados diretamente do dicionário principal
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Store está vazio, sincronizando dados de isolamento de outros stores
2025-05-24 13:44:14 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:14 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Dados de isolamento sincronizados e salvos no state_manager
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Store foi atualizado! Dados recebidos: <class 'dict'>
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Valores que serão enviados para os campos visuais:
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] potencia_mva: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] frequencia: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] grupo_ligacao: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] tensao_at: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] nbi_at: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] sil_at: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] tensao_bt: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] nbi_bt: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] sil_bt: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] teste_tensao_aplicada_at: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] teste_tensao_aplicada_bt: None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Dropdown values: nbi_at=None, sil_at=None, nbi_bt=None, sil_bt=None, nbi_terciario=None, sil_terciario=None
2025-05-24 13:44:14 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] nbi_at value before return: None
2025-05-24 13:44:15 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: potencia_mva
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: norma_iso. Norma: None, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: norma_iso. Norma: None, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:15 - utils.elec - ERROR - [elec] Erro inesperado no cálculo: float division by zero
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\elec.py", line 119, in calculate_nominal_currents
    result["corrente_nominal_at"] = round((potencia * 1000) / tensao_at, 2)
                                          ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
ZeroDivisionError: float division by zero
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: norma_iso. Norma: None, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: norma_iso. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: norma_iso. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: norma_iso. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_induzida_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 13:44:15 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:15 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 13:44:15 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-24 13:44:15 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-24 13:44:15 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] StateManager atualizado e salvo no disco.
2025-05-24 13:44:15 - callbacks.transformer_inputs - WARNING - [UpdateTransformerCalc] Dados insuficientes para propagação. Abortando.
2025-05-24 13:44:20 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 13:44:20 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/historico, clean_path=historico), prevenindo atualização
2025-05-24 13:44:20 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: None, Um_kv: None, Tensão: None, Path: /historico
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: None, Um_kv: None, Tensão: None, Path: /historico
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: None, Um_kv: None, Tensão: None, Path: /historico
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/historico
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/historico
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 13:44:20 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:20 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:20 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-24 13:44:21 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:21 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:21 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 13:44:25 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:25 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'temperature-rise-store'.
2025-05-24 13:44:25 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:25 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:25 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-24 13:44:27 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 13:44:27 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:27 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:27 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-24 13:44:31 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:31 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'impulse-store'.
2025-05-24 13:44:31 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:31 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'front-resistor-data'.
2025-05-24 13:44:31 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:31 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'tail-resistor-data'.
2025-05-24 13:44:31 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:31 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'calculated-inductance'.
2025-05-24 13:44:31 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 13:44:31 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'simulation-status'.
2025-05-24 13:44:31 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 13:44:31 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 13:44:31 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:03:24 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:03:24 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 18:03:24 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 18:03:24 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 18:03:24 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 18:03:24 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:03:24 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:03:24 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 18:03:24 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 18:03:24 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 18:03:24 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 18:03:29 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:03:29 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:03:29 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 18:03:29 - callbacks.global_updates - DEBUG - Painel atualizado em 2.0ms
2025-05-24 18:03:29 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 18:03:29 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:03:29 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:03:29 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:03:29 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 18:03:29 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:03:29 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:11:25 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:11:25 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 18:11:25 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 18:11:25 - __main__ - ERROR - Erro inesperado ao processar módulo de callback navigation_dcc_links: unexpected indent (impulse.py, line 410)
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 38, in <module>
    from layouts.impulse import create_impulse_layout  # Agora em impulse.py
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\impulse.py", line 410
    html.Script("""
IndentationError: unexpected indent
2025-05-24 18:11:25 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 18:11:25 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 18:11:25 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:11:25 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:11:25 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 18:11:25 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 18:11:25 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 18:11:25 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 18:11:28 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:11:28 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:11:28 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-24 18:11:29 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:11:29 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:11:29 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:28 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:21:28 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 18:21:28 - app - INFO - Callbacks registrados. Total: 49
2025-05-24 18:21:28 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 18:21:28 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 18:21:28 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:21:28 - callbacks.temperature_rise - INFO - Registrando callbacks do módulo temperature_rise para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 18:21:28 - callbacks.temperature_rise - INFO - Callbacks do módulo temperature_rise já registrados via decoradores @app.callback.
2025-05-24 18:21:28 - __main__ - INFO - Callbacks registrados. Total: 34
2025-05-24 18:21:28 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 18:21:28 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

2025-05-24 18:21:36 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:36 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-24 18:21:36 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:36 - callbacks.global_updates - DEBUG - Painel atualizado em 3.51ms
2025-05-24 18:21:36 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:36 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:36 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:37 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 18:21:37 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 18:21:38 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:38 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:38 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:38 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 18:21:38 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:38 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:43 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 18:21:43 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: transformer-inputs-store
2025-05-24 18:21:43 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:43 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Usando dados frescos do state_manager
2025-05-24 18:21:43 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:43 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Usando dados diretamente do dicionário principal
2025-05-24 18:21:43 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Store está vazio, sincronizando dados de isolamento de outros stores
2025-05-24 18:21:43 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 18:21:43 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Dados de isolamento sincronizados e salvos no state_manager
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Store foi atualizado! Dados recebidos: <class 'dict'>
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Valores que serão enviados para os campos visuais:
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] potencia_mva: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] frequencia: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] grupo_ligacao: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] tensao_at: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] nbi_at: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] sil_at: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] tensao_bt: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] nbi_bt: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] sil_bt: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] teste_tensao_aplicada_at: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] teste_tensao_aplicada_bt: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Dropdown values: nbi_at=None, sil_at=None, nbi_bt=None, sil_bt=None, nbi_terciario=None, sil_terciario=None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] nbi_at value before return: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] Acionado por: potencia_mva
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: norma_iso. Norma: None, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: norma_iso. Norma: None, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:21:43 - utils.elec - ERROR - [elec] Erro inesperado no cálculo: float division by zero
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\utils\elec.py", line 119, in calculate_nominal_currents
    result["corrente_nominal_at"] = round((potencia * 1000) / tensao_at, 2)
                                          ~~~~~~~~~~~~~~~~~~^~~~~~~~~~~
ZeroDivisionError: float division by zero
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: norma_iso. Norma: None, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: norma_iso. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: norma_iso. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: norma_iso. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_induzida_at com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_bt com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo nbi_neutro_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo sil_neutro_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - callbacks.transformer_inputs - INFO - [UpdateTransformerCalc] Sobrescrevendo teste_tensao_aplicada_terciario com None (campo limpo pelo usuário ou nunca definido)
2025-05-24 18:21:43 - app_core.transformer_mcp - WARNING - [MCP SET] Módulo utils.mcp_persistence não disponível para auto_propagate
2025-05-24 18:21:43 - app_core.transformer_mcp - DEBUG - [MCP SET] Dados definidos para store 'transformer-inputs-store'.
2025-05-24 18:21:43 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Salvando estado do MCP em disco...
2025-05-24 18:21:43 - app_core.transformer_mcp - INFO - [MCP SAVE_TO_DISK] Estado do MCP salvo em disco com sucesso.
2025-05-24 18:21:43 - callbacks.transformer_inputs - DEBUG - [UpdateTransformerCalc] StateManager atualizado e salvo no disco.
2025-05-24 18:21:43 - callbacks.transformer_inputs - WARNING - [UpdateTransformerCalc] Dados insuficientes para propagação. Abortando.
2025-05-24 18:21:50 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 18:21:50 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-24 18:21:50 - layouts.losses - INFO - Creating Losses layout...
2025-05-24 18:21:50 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-24 18:21:50 - layouts.losses - WARNING - [Losses Layout] Dados do transformador não encontrados.
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: None, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: None, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: None, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: None, Um_Neutro: None, Conexão: None, Tensão: None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:50 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 18:21:50 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:50 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:50 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:50 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-24 18:21:52 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 18:21:52 - callbacks.navigation_dcc_links - ERROR - Erro ao executar a função de layout para 'impulso': 'background_card_light'
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 190, in navigation_dcc_links_render_content
    layout_content = layout_function()  # Call the function
                     ^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\impulse.py", line 437, in create_impulse_layout
    style={"fontSize": "0.7rem", "maxHeight": "60px", "overflowY": "auto", "border": f"1px solid {COLORS['border']}", "padding": "3px", "marginTop": "3px", "backgroundColor": COLORS['background_card_light'], "color": COLORS['text_dark']}) # Estilo melhorado para tema claro
                                                                                                                                                                               ~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: 'background_card_light'
2025-05-24 18:21:53 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 18:21:53 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-24 18:21:53 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-24 18:21:53 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-24 18:21:53 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-24 18:21:53 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:53 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:53 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 18:21:53 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-24 18:21:54 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 18:21:54 - callbacks.navigation_dcc_links - ERROR - Erro ao executar a função de layout para 'impulso': 'background_card_light'
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 190, in navigation_dcc_links_render_content
    layout_content = layout_function()  # Call the function
                     ^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\impulse.py", line 437, in create_impulse_layout
    style={"fontSize": "0.7rem", "maxHeight": "60px", "overflowY": "auto", "border": f"1px solid {COLORS['border']}", "padding": "3px", "marginTop": "3px", "backgroundColor": COLORS['background_card_light'], "color": COLORS['text_dark']}) # Estilo melhorado para tema claro
                                                                                                                                                                               ~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: 'background_card_light'
2025-05-24 18:21:55 - layouts.losses - INFO - Creating Losses layout...
2025-05-24 18:21:55 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-24 18:21:55 - layouts.losses - WARNING - [Losses Layout] Dados do transformador não encontrados.
2025-05-24 18:21:55 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:55 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:55 - callbacks.global_updates - DEBUG - Painel atualizado em 2.51ms
2025-05-24 18:21:55 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-24 18:21:57 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 18:21:57 - callbacks.navigation_dcc_links - ERROR - Erro ao executar a função de layout para 'impulso': 'background_card_light'
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 190, in navigation_dcc_links_render_content
    layout_content = layout_function()  # Call the function
                     ^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\impulse.py", line 437, in create_impulse_layout
    style={"fontSize": "0.7rem", "maxHeight": "60px", "overflowY": "auto", "border": f"1px solid {COLORS['border']}", "padding": "3px", "marginTop": "3px", "backgroundColor": COLORS['background_card_light'], "color": COLORS['text_dark']}) # Estilo melhorado para tema claro
                                                                                                                                                                               ~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: 'background_card_light'
2025-05-24 18:21:58 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 18:21:58 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-24 18:21:58 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-24 18:21:58 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-24 18:21:58 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-24 18:21:58 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:58 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 18:21:58 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 18:21:58 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-24 18:21:59 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-24 18:21:59 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:21:59 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 18:21:59 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:21:59 - callbacks.global_updates - DEBUG - Painel atualizado em 3.09ms
2025-05-24 18:22:00 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 18:22:00 - layouts.induced_voltage - INFO - [Induced Voltage] Usando dados vazios inicialmente, serão preenchidos via callback
2025-05-24 18:22:00 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para frequencia-teste. Usando input_style.
2025-05-24 18:22:00 - components.ui_elements - WARNING - Style fornecido tanto via input_style quanto via kwargs para capacitancia. Usando input_style.
2025-05-24 18:22:00 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:22:00 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:22:00 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:22:01 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:22:01 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:22:01 - callbacks.global_updates - DEBUG - Painel atualizado em 3.51ms
2025-05-24 18:22:02 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:22:02 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:22:02 - callbacks.global_updates - DEBUG - Painel atualizado em 1.19ms
2025-05-24 18:22:03 - layouts.history - INFO - Criando layout de Histórico de Sessões (VERSÃO REFEITA)
2025-05-24 18:22:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:22:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:22:03 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 18:22:03 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-24 18:22:03 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 18:22:03 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 18:22:03 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-24 21:13:18 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 21:13:18 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 21:13:18 - layouts.standards_consultation - INFO - Criando layout de consulta de normas técnicas
2025-05-24 21:13:18 - callbacks.global_updates - DEBUG - Painel atualizado em 1.08ms
2025-05-24 21:13:18 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 21:13:18 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 21:13:18 - callbacks.global_updates - DEBUG - Painel atualizado em 0.0ms
2025-05-24 21:13:20 - layouts.transformer_inputs - INFO - Criando layout Dados Básicos (v5 - Layout Geral e Pesos em 2 linhas)...
2025-05-24 21:13:20 - layouts.transformer_inputs - INFO - Classes de tensão carregadas para dropdown: 27 opções.
2025-05-24 21:13:20 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 21:13:20 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 21:13:20 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 21:13:20 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: None
2025-05-24 21:13:20 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Opções dos dropdowns ainda não foram populadas, aguardando...
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_kv: None, Tensão: None, Path: /dados
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: initial_load_or_direct_call. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/dados
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 21:13:20 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:21 - callbacks.transformer_inputs - INFO - [LOAD TransformerInputs] Acionado por: url
2025-05-24 21:13:21 - callbacks.transformer_inputs - DEBUG - [LOAD TransformerInputs] Não estamos na página de dados (pathname=/perdas, clean_path=perdas), prevenindo atualização
2025-05-24 21:13:21 - layouts.losses - INFO - Creating Losses layout...
2025-05-24 21:13:21 - layouts.losses - INFO - [Losses Layout] Dados do transformador obtidos do losses-store.
2025-05-24 21:13:21 - layouts.losses - WARNING - [Losses Layout] Dados do transformador não encontrados.
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Acionado por: url. Norma: IEC, Um_kv: None, Tensão: None, Path: /perdas
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT] Valores salvos: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: estrela, Tensão: None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão: triangulo, Tensão: None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores do store: NBI=None, SIL=None, TA=None, TI=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Acionado por: url. Norma: IEC, Um_Neutro: None, Conexão:  , Tensão: None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - BT] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO] Valores salvos: NBI=None, SIL=None, TA=None, TI=/perdas
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores do store: NBI=None, SIL=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - AT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - BT-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - TERCIARIO] Um NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO - [OPTIONS CB - TERCIARIO-NEUTRO] Valores salvos: NBI=None, SIL=None
2025-05-24 21:13:21 - callbacks.insulation_level_callbacks - INFO -   [OPTIONS CB - AT-NEUTRO] Um_Neutro NÃO definido. Populando com opções GERAIS para Norma=IEC.
2025-05-24 21:13:21 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 21:13:21 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 21:13:21 - callbacks.global_updates - DEBUG - Painel atualizado em 2.13ms
2025-05-24 21:13:21 - callbacks.losses - CRITICAL - [LOSSES POPULATE CARGA] Acionado. Aba: tab-carga, Trigger: N/A
2025-05-24 21:13:22 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 21:13:22 - callbacks.navigation_dcc_links - ERROR - Erro ao executar a função de layout para 'impulso': 'background_card_light'
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 190, in navigation_dcc_links_render_content
    layout_content = layout_function()  # Call the function
                     ^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\impulse.py", line 437, in create_impulse_layout
KeyError: 'background_card_light'
2025-05-24 21:13:23 - layouts.dieletric_analysis - INFO - Creating Dielectric Analysis layout (from dieletric_analysis.py)...
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 21:13:23 - layouts.dieletric_analysis - WARNING - VerificadorTransformador is invalid upon creation in create_dielectric_layout.
2025-05-24 21:13:23 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Alta Tensão).
2025-05-24 21:13:23 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Baixa Tensão).
2025-05-24 21:13:23 - components.ui_elements - WARNING - Verificador inválido ao criar coluna dielétrica (Terciário).
2025-05-24 21:13:23 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 21:13:23 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - ERROR - Arquivo JSON 'c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\tabela.json' não encontrado.
2025-05-24 21:13:23 - app_core.standards - CRITICAL - Falha CRÍTICA ao inicializar as classes das normas: Falha ao carregar dados JSON para NBR ou IEEE.
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\standards.py", line 613, in __init__
    raise RuntimeError("Falha ao carregar dados JSON para NBR ou IEEE.")
RuntimeError: Falha ao carregar dados JSON para NBR ou IEEE.
2025-05-24 21:13:23 - callbacks.dieletric_analysis - ERROR - Instância VerificadorTransformador criada, mas inválida (falha dados?).
2025-05-24 21:13:24 - layouts.applied_voltage - INFO - [Applied Voltage Layout] Creating layout...
2025-05-24 21:13:24 - callbacks.global_updates - WARNING - Dados essenciais ausentes no state_manager
2025-05-24 21:13:24 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 821, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 21:13:24 - components.transformer_info_template - WARNING - transformer_data inválido ou vazio: {}
2025-05-24 21:13:24 - callbacks.global_updates - DEBUG - Painel atualizado em 1.0ms
2025-05-24 21:13:25 - app - ERROR - Exception on /_dash-update-component [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\dash.py", line 1373, in dispatch
    ctx.run(
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 465, in add_context
    output_value = _invoke_callback(func, *func_args, **func_kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\site-packages\dash\_callback.py", line 40, in _invoke_callback
    return func(*args, **kwargs)  # %% callback invoked %%
           ^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 784, in load_applied_voltage_inputs
    raise dash.no_update
TypeError: exceptions must derive from BaseException
2025-05-24 21:13:25 - layouts.impulse - INFO - Criando layout de impulso...
2025-05-24 21:13:25 - callbacks.navigation_dcc_links - ERROR - Erro ao executar a função de layout para 'impulso': 'background_card_light'
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\navigation_dcc_links.py", line 190, in navigation_dcc_links_render_content
    layout_content = layout_function()  # Call the function
                     ^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\layouts\impulse.py", line 437, in create_impulse_layout
KeyError: 'background_card_light'
