2025-05-23 23:28:40 - app - ERROR - <PERSON>rro inesperado ao processar módulo de callback losses: unexpected indent (losses.py, line 484) [app.py:202]
Traceback (most recent call last):
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 197, in <module>
    __import__(module_path)
  File "c:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\losses.py", line 484
    try:
IndentationError: unexpected indent
2025-05-24 21:12:43 - app - ERROR - Erro ao registrar callbacks de short_circuit explicitamente: invalid syntax (calculations.py, line 723) [app.py:224]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\short_circuit.py", line 14, in <module>
    from app_core.calculations import calculate_impedance_variation, calculate_short_circuit_params
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:12:43 - app - ERROR - Erro ao registrar callbacks de applied_voltage explicitamente: invalid syntax (calculations.py, line 723) [app.py:224]
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 23, in <module>
    from app_core.calculations import calculate_capacitive_load  # Função de cálculo principal
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
