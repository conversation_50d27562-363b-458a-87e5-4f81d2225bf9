# layouts/impulse.py
""" Define o layout para a seção de Simulação de Ensaios de Impulso. """
import dash_bootstrap_components as dbc
from dash import dcc, html
import logging # Adicionado para log
import plotly.graph_objects as go # Importação para criar gráficos
from layouts import COLORS, TYPOGRAPHY, COMPONENTS, SPACING

# Importar componentes reutilizáveis e constantes
# Certifique-se que os imports abaixo estão corretos e os módulos/constantes existem
try:
    from utils.constants import GENERATOR_CONFIGURATIONS, SHUNT_OPTIONS, INDUCTORS_OPTIONS
    from components.transformer_info_template import create_transformer_info_panel
    import config # Importar config para usar as constantes de configuração
    from app import app # Para acessar o cache da aplicação
except ImportError as e:
    log = logging.getLogger(__name__)
    log.error(f"Erro ao importar dependências em layouts/impulse.py: {e}")
    # Pode ser útil definir valores padrão ou retornar um layout de erro aqui
    GENERATOR_CONFIGURATIONS = [{"label": "Padrão", "value": "12S-1P"}] # Exemplo de fallback
    SHUNT_OPTIONS = [{"label": "0.01 Ω", "value": 0.01}] # Exemplo de fallback
    INDUCTORS_OPTIONS = [{"label": "Nenhum", "value": 0}] # Exemplo de fallback
    # A importação de 'config' geralmente é crucial, pode lançar um erro se falhar


# Estilos locais usando os estilos padronizados
section_title_style = TYPOGRAPHY['section_title']
graph_container_style = {
    "marginTop": "10px",
    "borderRadius": "5px",
    "overflow": "hidden",
    "border": f"1px solid {COLORS['border']}",
    "backgroundColor": COLORS['background_card'],
    "boxShadow": "0 1px 3px rgba(0,0,0,0.1)"
}
tab_label_style = {
    "fontSize": "0.75rem",
    "fontWeight": "bold",
    "padding": "0.25rem 0.5rem",
    "color": COLORS['text_light']
}

# Estilo para os headers dos cards
card_header_style = COMPONENTS['card_header']

def create_impulse_layout():
    """Creates the layout component for the Impulse Simulation section.

    Esta função cria o layout da seção de Simulação de Ensaios de Impulso e inclui
    o painel de informações do transformador diretamente no layout, obtendo
    os dados do cache da aplicação.

    Returns:
        dash.html.Div: O layout completo da seção de Simulação de Ensaios de Impulso
    """
    log = logging.getLogger(__name__)
    log.info("Criando layout de impulso...")
    from components.ui_elements import create_labeled_input
    from components.help_button import create_help_button
    # transformer_data será preenchido por callback, não aqui
    return dbc.Container([
        dcc.Location(id='url-impulse-section', refresh=False),
        dbc.Row([
            dbc.Col(
                html.Div([
                    html.Div(id="transformer-info-impulse-page", className="mb-1"),
                    html.Div(html.Div(), id="transformer-info-impulse", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-losses", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-dieletric", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-applied", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-induced", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-short-circuit", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-temperature-rise", style={"display": "none"}),
                    html.Div(html.Div(), id="transformer-info-comprehensive", style={"display": "none"}),
                ])
            , width=12)
        ], className="mb-2"),
        dbc.Card([
            dbc.CardHeader([
                html.Span("Simulação de Impulso", style={"fontWeight": "bold", "fontSize": "1.1rem", "color": COLORS["text_dark"]}),
                create_help_button("impulse"),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
            dbc.CardBody([
                create_labeled_input("Tensão de Pico (kV)", "impulse-peak-voltage", "number", "Ex: 1000", persistence=True, persistence_type="session"),
                create_labeled_input("Tempo de Subida (μs)", "impulse-rise-time", "number", "Ex: 1.2", persistence=True, persistence_type="session"),
                create_labeled_input("Tempo de Descida (μs)", "impulse-fall-time", "number", "Ex: 50", persistence=True, persistence_type="session"),
                html.Div(id="impulse-corrente-info", style={"fontSize": "0.8rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
                html.Div(id="impulse-result", style={"fontWeight": "bold", "fontSize": "0.9rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
        ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "boxShadow": "0 1px 3px rgba(0,0,0,0.07)", "border": f"1px solid {COLORS['border']}"}),
        dbc.Card([
            dbc.CardHeader("Gráfico de Impulso", style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "fontWeight": "bold", "fontSize": "1rem"}),
            dbc.CardBody([
                dcc.Graph(id="impulse-graph", config={"displayModeBar": False}, style={"backgroundColor": COLORS["background_card_light"]}),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
        ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "boxShadow": "0 1px 3px rgba(0,0,0,0.07)", "border": f"1px solid {COLORS['border']}"}),
    ], fluid=True, style={"backgroundColor": COLORS["background_light"], "color": COLORS["text_dark"]})
