2025-05-24 21:12:43 - app - ERROR - Erro ao registrar callbacks de temperature_rise explicitamente: invalid syntax (calculations.py, line 723)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\temperature_rise.py", line 13, in <module>
    from app_core.calculations import (
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:12:43 - app - INFO - Callbacks registrados. Total: 38
2025-05-24 21:12:43 - callbacks.insulation_level_callbacks - INFO - Registrando callbacks de níveis de isolamento (APENAS OPÇÕES - V2)...
2025-05-24 21:12:43 - callbacks.insulation_level_callbacks - INFO - Callbacks de níveis de isolamento (APENAS OPÇÕES - V2) registrados.
2025-05-24 21:12:43 - callbacks.transformer_inputs - INFO - Registrando callbacks do módulo transformer_inputs para app Simulador de Testes de Transformadores (IEC/IEEE/ABNT) EPS 1500...
2025-05-24 21:12:43 - __main__ - ERROR - Erro ao registrar callbacks de short_circuit explicitamente: invalid syntax (calculations.py, line 723)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\short_circuit.py", line 14, in <module>
    from app_core.calculations import calculate_impedance_variation, calculate_short_circuit_params
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:12:43 - __main__ - ERROR - Erro ao registrar callbacks de applied_voltage explicitamente: invalid syntax (calculations.py, line 723)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\applied_voltage.py", line 23, in <module>
    from app_core.calculations import calculate_capacitive_load  # Função de cálculo principal
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:12:43 - __main__ - ERROR - Erro ao registrar callbacks de temperature_rise explicitamente: invalid syntax (calculations.py, line 723)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app.py", line 215, in <module>
    module = __import__(module_path, fromlist=[reg_func_name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\callbacks\temperature_rise.py", line 13, in <module>
    from app_core.calculations import (
  File "C:\Users\<USER>\Downloads\transformer_simulator_corrigido\transformer_simulator_v2_complete\app_core\calculations.py", line 723
    idx2 = first_cross_idx + 1            if idx1 < len(t_after_peak) and idx2 < len(t_after_peak):
                                                                                                  ^
SyntaxError: invalid syntax
2025-05-24 21:12:43 - __main__ - INFO - Callbacks registrados. Total: 28
2025-05-24 21:12:43 - __main__ - INFO - Iniciando servidor Dash em http://127.0.0.1:8050/ (Debug: True)
2025-05-24 21:12:43 - dash.dash - INFO - Dash is running on http://127.0.0.1:8050/

