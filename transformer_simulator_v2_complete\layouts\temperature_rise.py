# layouts/temperature_rise.py
"""
Defines the layout for the Temperature Rise section as a function.
"""
import logging

import dash_bootstrap_components as dbc
from dash import dcc, html

from app import app  # Importa a instância app para acessar o cache de dados do transformador
from components.help_button import create_help_button
from components.transformer_info_template import create_transformer_info_panel

# Import reusable components and constants
from components.ui_elements import create_labeled_input
from utils import constants  # For material options

log = logging.getLogger(__name__)

# Importar estilos padronizados
from layouts import COLORS, COMPONENTS, SPACING, TYPOGRAPHY


# --- Layout Definition Function ---
def create_temperature_rise_layout():
    """Creates the layout component for the Temperature Rise section."""
    # transformer_data will be filled by callback, not here
    return dbc.Container(
        [
            dbc.Row([
                dbc.Col(
                    html.Div(
                        [
                            html.Div(id="transformer-info-temperature-rise-page", className="mb-1"),
                            html.Div(html.Div(), id="transformer-info-temperature-rise", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-losses", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-impulse", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-dieletric", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-applied", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-induced", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-short-circuit", style={"display": "none"}),
                            html.Div(html.Div(), id="transformer-info-comprehensive", style={"display": "none"}),
                        ]
                    )
                , width=12)
            ], className="mb-2"),
            dbc.Card([
                dbc.CardHeader([
                    html.Span("Elevação de Temperatura", style={"fontWeight": "bold", "fontSize": "1.1rem", "color": COLORS["text_dark"]}),
                    create_help_button("temperature-rise-help"),
                ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
                dbc.CardBody([
                    create_labeled_input("Tempo de Ensaio (min)", "temperature-rise-time", "Ex: 240", persistence=True, persistence_type="session"),
                    create_labeled_input("Temperatura Ambiente (°C)", "temperature-ambient", "Ex: 25", persistence=True, persistence_type="session"),
                    create_labeled_input("Temperatura Final (°C)", "temperature-final", "Ex: 75", persistence=True, persistence_type="session"),
                    html.Div(id="temperature-rise-corrente-info", style={"fontSize": "0.8rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
                    html.Div(id="temperature-rise-result", style={"fontWeight": "bold", "fontSize": "0.9rem", "color": COLORS["text_dark"], "marginTop": "0.5rem"}),
                ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"]}),
            ], style={"backgroundColor": COLORS["background_card_light"], "color": COLORS["text_dark"], "boxShadow": "0 1px 3px rgba(0,0,0,0.07)", "border": f"1px solid {COLORS['border']}"}),
        ], fluid=True, style={"backgroundColor": COLORS["background_light"], "color": COLORS["text_dark"]})
